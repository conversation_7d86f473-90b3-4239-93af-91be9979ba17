import 'dart:async';
import 'dart:collection';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/widgets.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/performance/performance_logging_config.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/responsive_system.dart';

/// Frame rate monitoring and jank detection system
///
/// This system provides:
/// - Real-time FPS tracking with 60/120 FPS support
/// - Jank detection with configurable thresholds
/// - Frame time analysis and optimization suggestions
/// - GPU rendering optimization monitoring
class FrameRateMonitor {
  bool _isInitialized = false;
  bool _isMonitoring = false;
  FrameRateConfig _config = FrameRateConfig.defaultConfig();

  // Frame timing data
  final Queue<Duration> _frameTimes = Queue<Duration>();
  final Queue<DateTime> _frameTimestamps = Queue<DateTime>();
  DateTime? _lastFrameTime;

  // Performance metrics
  double _currentFPS = 0.0;
  int _jankCount = 0;
  int _totalFrames = 0;
  Duration _totalFrameTime = Duration.zero;

  // Jank detection
  final List<JankEvent> _jankEvents = [];

  // Frame callback registration
  int? _frameCallbackId;

  /// Initialize the frame rate monitor
  Future<void> initialize(FrameRateConfig config) async {
    if (_isInitialized) return;

    _config = config;
    _isInitialized = true;

    // Log in both debug and profile modes
    AnxLog.info(
      '🎯 FrameRateMonitor: Initialized with target ${_config.targetFPS} FPS',
    );
  }

  /// Start frame rate monitoring
  void startMonitoring() {
    if (!_isInitialized || _isMonitoring) return;

    _isMonitoring = true;
    _resetMetrics();
    _startFrameCallback();

    // Log in both debug and profile modes
    AnxLog.info('🎯 FrameRateMonitor: Started monitoring');
  }

  /// Stop frame rate monitoring
  void stopMonitoring() {
    if (!_isMonitoring) return;

    _isMonitoring = false;
    _stopFrameCallback();

    if (kDebugMode) {
      AnxLog.info('🎯 FrameRateMonitor: Stopped monitoring');
    }
  }

  /// Get current frame rate metrics
  FrameRateMetrics getMetrics() {
    return FrameRateMetrics(
      currentFPS: _currentFPS,
      averageFPS: _calculateAverageFPS(),
      targetFPS: _config.targetFPS,
      jankCount: _jankCount,
      totalFrames: _totalFrames,
      jankEvents: List.from(_jankEvents),
      performanceScore: _calculatePerformanceScore(),
    );
  }

  /// Get current FPS
  double get currentFPS => _currentFPS;

  /// Check if currently experiencing jank
  bool get isJanky =>
      _frameTimes.isNotEmpty &&
      _frameTimes.last.inMicroseconds > _config.jankThresholdMicroseconds;

  /// Start frame timing callback
  void _startFrameCallback() {
    _frameCallbackId =
        SchedulerBinding.instance.scheduleFrameCallback(_onFrame);
  }

  /// Stop frame timing callback
  void _stopFrameCallback() {
    if (_frameCallbackId != null) {
      SchedulerBinding.instance.cancelFrameCallbackWithId(_frameCallbackId!);
      _frameCallbackId = null;
    }
  }

  /// Handle frame callback
  void _onFrame(Duration timestamp) {
    if (!_isMonitoring) return;

    final now = DateTime.now();

    if (_lastFrameTime != null) {
      final frameTime = now.difference(_lastFrameTime!);
      _processFrame(frameTime, now);
    }

    _lastFrameTime = now;
    _totalFrames++;

    // Schedule next frame callback
    if (_isMonitoring) {
      _frameCallbackId =
          SchedulerBinding.instance.scheduleFrameCallback(_onFrame);
    }
  }

  /// Process individual frame timing
  void _processFrame(Duration frameTime, DateTime timestamp) {
    // Add to frame time history
    _frameTimes.add(frameTime);
    _frameTimestamps.add(timestamp);
    _totalFrameTime += frameTime;

    // Maintain sliding window
    while (_frameTimes.length > _config.frameHistorySize) {
      _totalFrameTime -= _frameTimes.removeFirst();
      _frameTimestamps.removeFirst();
    }

    // Calculate current FPS
    _currentFPS = _calculateCurrentFPS();

    // Check for jank
    _checkForJank(frameTime, timestamp);

    // Log performance issues in debug and profile modes
    if (!kReleaseMode && _config.enableDebugLogging) {
      _logPerformanceIssues(frameTime);
    }
  }

  /// Calculate current FPS based on recent frames
  double _calculateCurrentFPS() {
    if (_frameTimes.isEmpty) return 0.0;

    // Use a sliding window of recent frames for more stable FPS calculation
    final windowSize = math.min(_frameTimes.length, 30); // Last 30 frames
    final skipCount = _frameTimes.length - windowSize;
    final recentFrames = _frameTimes.skip(skipCount);

    final totalTime = recentFrames.fold<Duration>(
      Duration.zero,
      (sum, frameTime) => sum + frameTime,
    );

    if (totalTime.inMicroseconds == 0) return 0.0;

    // Calculate FPS: frames per second = (frame_count * 1_000_000) / total_microseconds
    final fps = (windowSize * 1000000) / totalTime.inMicroseconds;

    // Clamp to reasonable values to prevent calculation errors
    return fps.clamp(0.0, 240.0); // Max 240 FPS to catch calculation errors
  }

  /// Calculate average FPS over entire monitoring period
  double _calculateAverageFPS() {
    if (_totalFrames == 0 || _totalFrameTime.inMicroseconds == 0) return 0.0;

    // Calculate average FPS with protection against extreme values
    final avgFPS = (_totalFrames * 1000000) / _totalFrameTime.inMicroseconds;

    // Clamp to reasonable values to prevent calculation errors
    return avgFPS.clamp(0.0, 240.0); // Max 240 FPS to catch calculation errors
  }

  /// Check for jank and record events with enhanced detection
  void _checkForJank(Duration frameTime, DateTime timestamp) {
    final frameMicroseconds = frameTime.inMicroseconds;

    if (frameMicroseconds > _config.jankThresholdMicroseconds) {
      _jankCount++;

      final severity = _getJankSeverity(frameMicroseconds);
      final jankEvent = JankEvent(
        timestamp: timestamp,
        frameTime: frameTime,
        severity: severity,
        expectedFrameTime:
            Duration(microseconds: (1000000 / _config.targetFPS).round()),
      );

      _jankEvents.add(jankEvent);

      // Maintain jank event history
      while (_jankEvents.length > _config.maxJankEvents) {
        _jankEvents.removeAt(0);
      }

      // Enhanced logging with context and suggestions
      if (!kReleaseMode && PerformanceLoggingConfig.enableJankLogging) {
        _logJankWithContext(frameTime, severity, timestamp);
      }
    }
  }

  /// Enhanced jank logging with context and optimization suggestions
  void _logJankWithContext(
    Duration frameTime,
    JankSeverity severity,
    DateTime timestamp,
  ) {
    final ms = frameTime.inMilliseconds;
    final fps = (1000 / ms).toStringAsFixed(1);

    // Detect jank patterns
    final recentJankCount = _jankEvents
        .where((event) => timestamp.difference(event.timestamp).inSeconds < 5)
        .length;

    String context = '';
    if (recentJankCount > 5) {
      context = ' [PATTERN: $recentJankCount janks in 5s]';
    }

    // Provide optimization hints based on severity
    String hint = '';
    switch (severity) {
      case JankSeverity.severe:
        if (ms > 100) {
          hint =
              ' 💡 Check for heavy computations, large widget builds, or I/O operations';
        }
        break;
      case JankSeverity.moderate:
        if (ms > 33) {
          hint = ' 💡 Consider async operations or widget optimization';
        }
        break;
      case JankSeverity.mild:
        if (recentJankCount > 3) {
          hint = ' 💡 Frequent mild jank - check for inefficient rebuilds';
        }
        break;
    }

    // Enhanced logging with stack trace context for debugging
    final stackTrace = StackTrace.current;
    final stackLines = stackTrace.toString().split('\n');
    final relevantStack = stackLines
        .where(
          (line) =>
              line.contains('dasso_reader') &&
              !line.contains('frame_rate_monitor'),
        )
        .take(3)
        .join(' -> ');

    AnxLog.warning(
      '🐌 Jank detected: ${ms}ms ($fps FPS, ${severity.name})$context$hint',
    );

    if (relevantStack.isNotEmpty && !kReleaseMode) {
      AnxLog.info('📍 Jank location: $relevantStack');
    }

    // Track jank sources for pattern analysis
    _trackJankSource(ms, relevantStack);
  }

  /// Track jank sources to identify common culprits
  final Map<String, int> _jankSources = {};

  void _trackJankSource(int frameTimeMs, String stackTrace) {
    if (stackTrace.isEmpty) return;

    // Extract the most relevant part of the stack trace
    final parts = stackTrace.split(' -> ');
    if (parts.isNotEmpty) {
      final source = parts.first.trim();
      _jankSources[source] = (_jankSources[source] ?? 0) + 1;

      // Log frequent jank sources
      if (_jankSources[source]! > 5 && !kReleaseMode) {
        AnxLog.warning(
          '🔥 Frequent jank source detected: $source (${_jankSources[source]} times)',
        );
      }
    }
  }

  /// Get jank source analysis
  Map<String, int> getJankSourceAnalysis() {
    return Map.from(_jankSources);
  }

  /// Determine jank severity based on frame time
  JankSeverity _getJankSeverity(int frameMicroseconds) {
    final expectedMicroseconds = (1000000 / _config.targetFPS).round();
    final multiplier = frameMicroseconds / expectedMicroseconds;

    if (multiplier >= 4.0) return JankSeverity.severe;
    if (multiplier >= 2.0) return JankSeverity.moderate;
    return JankSeverity.mild;
  }

  /// Calculate performance score (0-100)
  double _calculatePerformanceScore() {
    if (_totalFrames == 0) return 100.0;

    final averageFPS = _calculateAverageFPS();
    final fpsScore = (averageFPS / _config.targetFPS * 100).clamp(0.0, 100.0);

    // Penalize for jank events
    final jankPenalty = (_jankCount / _totalFrames * 100 * 2).clamp(0.0, 50.0);

    return (fpsScore - jankPenalty).clamp(0.0, 100.0);
  }

  /// Log performance issues in debug mode
  void _logPerformanceIssues(Duration frameTime) {
    final expectedFrameTime =
        Duration(microseconds: (1000000 / _config.targetFPS).round());

    if (frameTime > expectedFrameTime * 1.5) {
      final fps = 1000000 / frameTime.inMicroseconds;
      AnxLog.info(
        '⚠️ Frame performance: ${frameTime.inMilliseconds}ms (${fps.toStringAsFixed(1)} FPS)',
      );
    }
  }

  /// Reset all metrics
  void _resetMetrics() {
    _frameTimes.clear();
    _frameTimestamps.clear();
    _jankEvents.clear();
    _currentFPS = 0.0;
    _jankCount = 0;
    _totalFrames = 0;
    _totalFrameTime = Duration.zero;
    _lastFrameTime = null;
  }

  /// Dispose of resources
  void dispose() {
    stopMonitoring();
    _resetMetrics();
    _isInitialized = false;

    if (kDebugMode) {
      AnxLog.info('🎯 FrameRateMonitor: Disposed');
    }
  }
}

/// Frame rate monitoring configuration
class FrameRateConfig {
  const FrameRateConfig({
    required this.targetFPS,
    required this.jankThresholdMicroseconds,
    required this.frameHistorySize,
    required this.maxJankEvents,
    required this.enableDebugLogging,
    required this.enableJankLogging,
  });

  final double targetFPS;
  final int jankThresholdMicroseconds;
  final int frameHistorySize;
  final int maxJankEvents;
  final bool enableDebugLogging;
  final bool enableJankLogging;

  factory FrameRateConfig.defaultConfig() {
    return const FrameRateConfig(
      targetFPS: 60.0,
      jankThresholdMicroseconds: 16670, // 16.67ms for 60 FPS
      frameHistorySize: 120, // 2 seconds at 60 FPS
      maxJankEvents: 100,
      enableDebugLogging: false, // Disabled for cleaner development experience
      enableJankLogging: false, // Disabled for cleaner development experience
    );
  }

  /// Development config with verbose logging for performance debugging
  factory FrameRateConfig.verboseConfig() {
    return const FrameRateConfig(
      targetFPS: 60.0,
      jankThresholdMicroseconds: 16670, // 16.67ms for 60 FPS
      frameHistorySize: 120, // 2 seconds at 60 FPS
      maxJankEvents: 100,
      enableDebugLogging: true, // Enable for performance debugging
      enableJankLogging: true, // Enable for performance debugging
    );
  }

  factory FrameRateConfig.highRefreshRate() {
    return const FrameRateConfig(
      targetFPS: 120.0,
      jankThresholdMicroseconds: 8330, // 8.33ms for 120 FPS
      frameHistorySize: 240, // 2 seconds at 120 FPS
      maxJankEvents: 100,
      enableDebugLogging: false, // Disabled for cleaner development experience
      enableJankLogging: false, // Disabled for cleaner development experience
    );
  }
}

/// Frame rate metrics data
class FrameRateMetrics {
  const FrameRateMetrics({
    required this.currentFPS,
    required this.averageFPS,
    required this.targetFPS,
    required this.jankCount,
    required this.totalFrames,
    required this.jankEvents,
    required this.performanceScore,
  });

  final double currentFPS;
  final double averageFPS;
  final double targetFPS;
  final int jankCount;
  final int totalFrames;
  final List<JankEvent> jankEvents;
  final double performanceScore;

  /// Get jank rate as percentage
  double get jankRate =>
      totalFrames > 0 ? (jankCount / totalFrames * 100) : 0.0;

  /// Check if performance is acceptable
  bool get isPerformanceAcceptable => performanceScore >= 70.0;
}

/// Jank event data
class JankEvent {
  const JankEvent({
    required this.timestamp,
    required this.frameTime,
    required this.severity,
    required this.expectedFrameTime,
  });

  final DateTime timestamp;
  final Duration frameTime;
  final JankSeverity severity;
  final Duration expectedFrameTime;

  /// Get frame time multiplier compared to expected
  double get frameTimeMultiplier =>
      frameTime.inMicroseconds / expectedFrameTime.inMicroseconds;
}

/// Jank severity levels
enum JankSeverity {
  mild,
  moderate,
  severe,
}

/// Tablet-specific performance monitoring system
///
/// Extends FrameRateMonitor with tablet-specific capabilities:
/// - Form factor-aware performance thresholds
/// - NavigationRail-specific performance tracking
/// - Master-detail layout performance monitoring
/// - Enhanced jank analysis for larger screen layouts
class TabletPerformanceMonitor extends FrameRateMonitor {
  // Tablet-specific metrics
  final Map<String, Duration> _componentRenderTimes = {};
  final List<TabletJankEvent> _tabletJankEvents = [];
  final Map<String, int> _layoutComplexityMetrics = {};

  // Form factor detection
  bool _isTabletMode = false;
  bool _isLandscapeMode = false;

  /// Initialize tablet-specific performance monitoring
  @override
  Future<void> initialize(FrameRateConfig config) async {
    await super.initialize(config);

    // Initialize tablet-specific monitoring
    _initializeTabletMetrics();
    _setupFormFactorMonitoring();

    if (kDebugMode) {
      AnxLog.info(
          '📱 TabletPerformanceMonitor: Initialized with tablet-specific monitoring');
    }
  }

  /// Setup automatic form factor monitoring
  void _setupFormFactorMonitoring() {
    // Form factor detection will be handled by TabletPerformanceUtils
    // when updateFormFactor is called from UI components
    if (kDebugMode) {
      AnxLog.info('📱 TabletPerformanceMonitor: Form factor monitoring ready');
    }
  }

  /// Initialize tablet-specific metrics collection
  void _initializeTabletMetrics() {
    _componentRenderTimes.clear();
    _tabletJankEvents.clear();
    _layoutComplexityMetrics.clear();

    // Initialize component tracking
    _componentRenderTimes.addAll({
      'navigationRail': Duration.zero,
      'masterDetailLayout': Duration.zero,
      'sideMenu': Duration.zero,
      'searchBar': Duration.zero,
      'profilePane': Duration.zero,
      'webView': Duration.zero,
    });

    // Initialize layout complexity metrics
    _layoutComplexityMetrics.addAll({
      'widgetTreeDepth': 0,
      'activeProviders': 0,
      'simultaneousAnimations': 0,
      'visibleComponents': 0,
    });
  }

  /// Update form factor detection
  void updateFormFactor(bool isTablet, bool isLandscape) {
    final wasTabletMode = _isTabletMode;
    final wasLandscapeMode = _isLandscapeMode;

    _isTabletMode = isTablet;
    _isLandscapeMode = isLandscape;

    // Track layout transitions for performance impact
    if (wasTabletMode != isTablet || wasLandscapeMode != isLandscape) {
      _trackLayoutTransition(
          wasTabletMode, wasLandscapeMode, isTablet, isLandscape);
    }

    // Log form factor changes for debugging
    if (kDebugMode &&
        (wasTabletMode != isTablet || wasLandscapeMode != isLandscape)) {
      AnxLog.info('📱 TabletPerformanceMonitor: Form factor updated - '
          'Tablet: $isTablet, Landscape: $isLandscape');
    }
  }

  /// Track layout transition performance impact
  void _trackLayoutTransition(
      bool wasTablet, bool wasLandscape, bool isTablet, bool isLandscape) {
    final transitionType =
        _getTransitionType(wasTablet, wasLandscape, isTablet, isLandscape);

    // Start tracking transition performance
    final stopwatch = Stopwatch()..start();

    // Schedule post-frame callback to measure transition time
    WidgetsBinding.instance.addPostFrameCallback((_) {
      stopwatch.stop();
      final transitionTime = stopwatch.elapsed;

      // Track transition as a component render time
      trackComponentRenderTime(
          'layoutTransition_$transitionType', transitionTime);

      if (kDebugMode && transitionTime.inMilliseconds > 100) {
        AnxLog.warning(
            '📱 Layout transition ($transitionType) took ${transitionTime.inMilliseconds}ms');
      }
    });
  }

  /// Get transition type description
  String _getTransitionType(
      bool wasTablet, bool wasLandscape, bool isTablet, bool isLandscape) {
    if (wasTablet != isTablet) {
      return isTablet ? 'mobile_to_tablet' : 'tablet_to_mobile';
    }
    if (wasLandscape != isLandscape) {
      return isLandscape ? 'portrait_to_landscape' : 'landscape_to_portrait';
    }
    return 'unknown';
  }

  /// Track responsive layout performance
  void trackResponsiveLayoutPerformance({
    required String layoutType,
    required Duration renderTime,
    required Map<String, dynamic> layoutMetrics,
  }) {
    if (!_isTabletMode) return; // Only track on tablets

    // Track layout-specific render time
    trackComponentRenderTime('responsiveLayout_$layoutType', renderTime);

    // Extract layout complexity from metrics
    final breakpointCount = layoutMetrics['breakpointCount'] as int? ?? 0;
    final adaptiveComponents = layoutMetrics['adaptiveComponents'] as int? ?? 0;
    final conditionalLayouts = layoutMetrics['conditionalLayouts'] as int? ?? 0;

    // Update layout complexity metrics
    trackLayoutComplexity(
      visibleComponents: adaptiveComponents,
      widgetTreeDepth: breakpointCount * 2, // Estimate based on breakpoints
    );

    if (kDebugMode && renderTime.inMilliseconds > 25) {
      AnxLog.warning(
          '📱 Responsive layout ($layoutType) render time: ${renderTime.inMilliseconds}ms');
    }
  }

  /// Track component render time
  void trackComponentRenderTime(String component, Duration renderTime) {
    if (!_isTabletMode) return; // Only track on tablets

    _componentRenderTimes[component] = renderTime;

    // Check for component-specific performance issues
    _analyzeComponentPerformance(component, renderTime);
  }

  /// Track layout complexity metrics
  void trackLayoutComplexity({
    int? widgetTreeDepth,
    int? activeProviders,
    int? simultaneousAnimations,
    int? visibleComponents,
  }) {
    if (!_isTabletMode) return; // Only track on tablets

    if (widgetTreeDepth != null) {
      _layoutComplexityMetrics['widgetTreeDepth'] = widgetTreeDepth;
    }
    if (activeProviders != null) {
      _layoutComplexityMetrics['activeProviders'] = activeProviders;
    }
    if (simultaneousAnimations != null) {
      _layoutComplexityMetrics['simultaneousAnimations'] =
          simultaneousAnimations;
    }
    if (visibleComponents != null) {
      _layoutComplexityMetrics['visibleComponents'] = visibleComponents;
    }
  }

  /// Analyze component-specific performance
  void _analyzeComponentPerformance(String component, Duration renderTime) {
    final thresholds = _getComponentThresholds();
    final threshold = thresholds[component] ?? const Duration(milliseconds: 16);

    if (renderTime > threshold) {
      final tabletJankEvent = TabletJankEvent(
        timestamp: DateTime.now(),
        component: component,
        renderTime: renderTime,
        threshold: threshold,
        formFactor: _isTabletMode ? 'tablet' : 'mobile',
        orientation: _isLandscapeMode ? 'landscape' : 'portrait',
        layoutComplexity: Map<String, int>.from(_layoutComplexityMetrics),
      );

      _tabletJankEvents.add(tabletJankEvent);

      // Maintain event history
      while (_tabletJankEvents.length > 50) {
        _tabletJankEvents.removeAt(0);
      }

      // Log tablet-specific jank events
      if (kDebugMode && PerformanceLoggingConfig.enableJankLogging) {
        _logTabletJankEvent(tabletJankEvent);
      }
    }
  }

  /// Get component-specific performance thresholds
  Map<String, Duration> _getComponentThresholds() {
    // Tablet-specific thresholds (more lenient due to complexity)
    if (_isTabletMode) {
      return {
        'navigationRail':
            const Duration(milliseconds: 20), // 20ms for NavigationRail
        'masterDetailLayout':
            const Duration(milliseconds: 25), // 25ms for master-detail
        'sideMenu': const Duration(milliseconds: 18), // 18ms for side menu
        'searchBar': const Duration(milliseconds: 15), // 15ms for search bar
        'profilePane':
            const Duration(milliseconds: 22), // 22ms for profile pane
        'webView':
            const Duration(milliseconds: 30), // 30ms for WebView rendering
      };
    }

    // Mobile thresholds (stricter)
    return {
      'navigationRail': const Duration(milliseconds: 16),
      'masterDetailLayout': const Duration(milliseconds: 16),
      'sideMenu': const Duration(milliseconds: 16),
      'searchBar': const Duration(milliseconds: 16),
      'profilePane': const Duration(milliseconds: 16),
      'webView': const Duration(milliseconds: 20),
    };
  }

  /// Log tablet-specific jank events
  void _logTabletJankEvent(TabletJankEvent event) {
    final ms = event.renderTime.inMicroseconds / 1000;
    final thresholdMs = event.threshold.inMicroseconds / 1000;

    AnxLog.warning(
        '📱 TabletJank: ${event.component} took ${ms.toStringAsFixed(1)}ms '
        '(threshold: ${thresholdMs.toStringAsFixed(1)}ms) '
        'on ${event.formFactor} ${event.orientation}');

    // Provide tablet-specific optimization hints
    final hint = _getTabletOptimizationHint(event.component, event.renderTime);
    if (hint.isNotEmpty) {
      AnxLog.info('💡 TabletOptimization: $hint');
    }
  }

  /// Get tablet-specific optimization hints
  String _getTabletOptimizationHint(String component, Duration renderTime) {
    final ms = renderTime.inMicroseconds / 1000;

    switch (component) {
      case 'navigationRail':
        if (ms > 30) {
          return 'Consider RepaintBoundary around NavigationRail or lazy loading menu items';
        }
        return 'Check NavigationRail rebuild frequency and state management';

      case 'masterDetailLayout':
        if (ms > 40) {
          return 'Use IndexedStack for master-detail layout or implement lazy loading';
        }
        return 'Optimize Riverpod provider rebuilds in master-detail layout';

      case 'sideMenu':
        return 'Implement lazy loading for side menu components or reduce animation complexity';

      case 'searchBar':
        return 'Use AnimatedBuilder instead of AnimatedSwitcher for search bar animations';

      case 'profilePane':
        return 'Optimize profile pane state management and reduce widget tree depth';

      case 'webView':
        if (ms > 50) {
          return 'Consider WebView optimization for tablet screens or progressive loading';
        }
        return 'Check WebView-Flutter bridge performance on larger screens';

      default:
        return 'Consider component-specific optimization strategies for tablets';
    }
  }

  /// Get tablet-specific performance metrics
  TabletPerformanceMetrics getTabletMetrics() {
    final baseMetrics = getMetrics();

    return TabletPerformanceMetrics(
      // Base metrics
      currentFPS: baseMetrics.currentFPS,
      averageFPS: baseMetrics.averageFPS,
      targetFPS: baseMetrics.targetFPS,
      jankCount: baseMetrics.jankCount,
      totalFrames: baseMetrics.totalFrames,
      jankEvents: baseMetrics.jankEvents,
      performanceScore: baseMetrics.performanceScore,

      // Tablet-specific metrics
      isTabletMode: _isTabletMode,
      isLandscapeMode: _isLandscapeMode,
      componentRenderTimes: Map.from(_componentRenderTimes),
      tabletJankEvents: List.from(_tabletJankEvents),
      layoutComplexityMetrics: Map.from(_layoutComplexityMetrics),
      tabletPerformanceScore: _calculateTabletPerformanceScore(),
    );
  }

  /// Calculate tablet-specific performance score
  double _calculateTabletPerformanceScore() {
    if (!_isTabletMode) return getMetrics().performanceScore;

    final baseScore = getMetrics().performanceScore;

    // Adjust score based on tablet-specific factors
    double tabletAdjustment = 0.0;

    // Component performance adjustment
    final componentPenalty = _calculateComponentPenalty();
    tabletAdjustment -= componentPenalty;

    // Layout complexity adjustment
    final complexityPenalty = _calculateComplexityPenalty();
    tabletAdjustment -= complexityPenalty;

    // Form factor bonus (tablets are expected to perform slightly worse)
    tabletAdjustment += 5.0; // 5 point bonus for tablet complexity

    return (baseScore + tabletAdjustment).clamp(0.0, 100.0);
  }

  /// Calculate component performance penalty
  double _calculateComponentPenalty() {
    double penalty = 0.0;
    final thresholds = _getComponentThresholds();

    _componentRenderTimes.forEach((component, renderTime) {
      final threshold =
          thresholds[component] ?? const Duration(milliseconds: 16);
      if (renderTime > threshold) {
        final overageMs = (renderTime - threshold).inMicroseconds / 1000;
        penalty += overageMs * 0.5; // 0.5 points per ms overage
      }
    });

    return penalty.clamp(0.0, 20.0); // Max 20 point penalty
  }

  /// Calculate layout complexity penalty
  double _calculateComplexityPenalty() {
    double penalty = 0.0;

    // Widget tree depth penalty
    final treeDepth = _layoutComplexityMetrics['widgetTreeDepth'] ?? 0;
    if (treeDepth > 15) {
      penalty += (treeDepth - 15) * 0.5; // 0.5 points per excessive level
    }

    // Active providers penalty
    final activeProviders = _layoutComplexityMetrics['activeProviders'] ?? 0;
    if (activeProviders > 10) {
      penalty +=
          (activeProviders - 10) * 0.3; // 0.3 points per excessive provider
    }

    // Simultaneous animations penalty
    final animations = _layoutComplexityMetrics['simultaneousAnimations'] ?? 0;
    if (animations > 3) {
      penalty += (animations - 3) * 1.0; // 1 point per excessive animation
    }

    return penalty.clamp(0.0, 15.0); // Max 15 point penalty
  }
}

/// Tablet-specific jank event data
class TabletJankEvent {
  const TabletJankEvent({
    required this.timestamp,
    required this.component,
    required this.renderTime,
    required this.threshold,
    required this.formFactor,
    required this.orientation,
    required this.layoutComplexity,
  });

  final DateTime timestamp;
  final String component;
  final Duration renderTime;
  final Duration threshold;
  final String formFactor;
  final String orientation;
  final Map<String, int> layoutComplexity;

  /// Get severity based on render time vs threshold
  JankSeverity get severity {
    final ratio = renderTime.inMicroseconds / threshold.inMicroseconds;
    if (ratio > 3.0) return JankSeverity.severe;
    if (ratio > 2.0) return JankSeverity.moderate;
    return JankSeverity.mild;
  }

  /// Get performance impact description
  String get impactDescription {
    final ms = renderTime.inMicroseconds / 1000;
    final thresholdMs = threshold.inMicroseconds / 1000;
    final overage = ms - thresholdMs;

    return '$component exceeded threshold by ${overage.toStringAsFixed(1)}ms '
        'on $formFactor $orientation';
  }
}

/// Tablet-specific performance metrics
class TabletPerformanceMetrics extends FrameRateMetrics {
  const TabletPerformanceMetrics({
    // Base metrics
    required double currentFPS,
    required double averageFPS,
    required double targetFPS,
    required int jankCount,
    required int totalFrames,
    required List<JankEvent> jankEvents,
    required double performanceScore,

    // Tablet-specific metrics
    required this.isTabletMode,
    required this.isLandscapeMode,
    required this.componentRenderTimes,
    required this.tabletJankEvents,
    required this.layoutComplexityMetrics,
    required this.tabletPerformanceScore,
  }) : super(
          currentFPS: currentFPS,
          averageFPS: averageFPS,
          targetFPS: targetFPS,
          jankCount: jankCount,
          totalFrames: totalFrames,
          jankEvents: jankEvents,
          performanceScore: performanceScore,
        );

  final bool isTabletMode;
  final bool isLandscapeMode;
  final Map<String, Duration> componentRenderTimes;
  final List<TabletJankEvent> tabletJankEvents;
  final Map<String, int> layoutComplexityMetrics;
  final double tabletPerformanceScore;

  /// Get tablet jank rate (percentage)
  double get tabletJankRate {
    if (tabletJankEvents.isEmpty) return 0.0;

    // Calculate jank rate based on tablet-specific events
    final recentEvents = tabletJankEvents
        .where(
          (event) => DateTime.now().difference(event.timestamp).inMinutes < 5,
        )
        .length;

    return (recentEvents / tabletJankEvents.length * 100).clamp(0.0, 100.0);
  }

  /// Get worst performing component
  String get worstPerformingComponent {
    if (componentRenderTimes.isEmpty) return 'none';

    String worstComponent = 'none';
    Duration longestTime = Duration.zero;

    componentRenderTimes.forEach((component, renderTime) {
      if (renderTime > longestTime) {
        longestTime = renderTime;
        worstComponent = component;
      }
    });

    return worstComponent;
  }

  /// Get layout complexity score (0-100, lower is better)
  double get layoutComplexityScore {
    double score = 0.0;

    // Widget tree depth (0-30 points)
    final treeDepth = layoutComplexityMetrics['widgetTreeDepth'] ?? 0;
    score += (treeDepth / 20 * 30).clamp(0.0, 30.0);

    // Active providers (0-25 points)
    final activeProviders = layoutComplexityMetrics['activeProviders'] ?? 0;
    score += (activeProviders / 15 * 25).clamp(0.0, 25.0);

    // Simultaneous animations (0-25 points)
    final animations = layoutComplexityMetrics['simultaneousAnimations'] ?? 0;
    score += (animations / 5 * 25).clamp(0.0, 25.0);

    // Visible components (0-20 points)
    final visibleComponents = layoutComplexityMetrics['visibleComponents'] ?? 0;
    score += (visibleComponents / 10 * 20).clamp(0.0, 20.0);

    return score.clamp(0.0, 100.0);
  }

  /// Get performance summary for debugging
  Map<String, dynamic> getPerformanceSummary() {
    return {
      'formFactor': isTabletMode ? 'tablet' : 'mobile',
      'orientation': isLandscapeMode ? 'landscape' : 'portrait',
      'currentFPS': currentFPS,
      'tabletPerformanceScore': tabletPerformanceScore,
      'tabletJankRate': tabletJankRate,
      'worstComponent': worstPerformingComponent,
      'layoutComplexityScore': layoutComplexityScore,
      'recentTabletJankEvents': tabletJankEvents
          .where((event) =>
              DateTime.now().difference(event.timestamp).inMinutes < 5)
          .length,
      'componentPerformance': componentRenderTimes.map(
        (component, renderTime) => MapEntry(
          component,
          '${(renderTime.inMicroseconds / 1000).toStringAsFixed(1)}ms',
        ),
      ),
    };
  }
}
