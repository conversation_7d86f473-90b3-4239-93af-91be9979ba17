import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/responsive_system.dart';
import 'package:dasso_reader/utils/performance/tablet_performance_utils.dart';

/// Optimized layout transition system for master-detail layouts
/// 
/// Provides smooth, performance-aware transitions for:
/// - Orientation changes (portrait ↔ landscape)
/// - Profile pane show/hide animations
/// - NavigationRail layout transitions
/// - PageController optimizations
class LayoutTransitionOptimizer {
  static final TabletPerformanceUtils _performanceUtils = TabletPerformanceUtils.instance;
  
  // Animation controllers cache
  static final Map<String, AnimationController> _controllers = {};
  
  // Transition configurations
  static const Duration _fastTransition = Duration(milliseconds: 200);
  static const Duration _normalTransition = Duration(milliseconds: 300);
  static const Duration _slowTransition = Duration(milliseconds: 400);
  
  /// Create optimized profile pane show animation
  static Widget createProfilePaneTransition({
    required Widget child,
    required bool isVisible,
    required TickerProvider vsync,
    String? key,
  }) {
    final animationKey = key ?? 'profilePane';
    
    // Get or create animation controller
    final controller = _getOrCreateController(
      animationKey,
      _normalTransition,
      vsync,
    );
    
    // Track animation performance
    _performanceUtils.startComponentTracking('profilePaneAnimation');
    
    // Animate based on visibility
    if (isVisible) {
      controller.forward();
    } else {
      controller.reverse();
    }
    
    // Create optimized transition
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        final animation = CurvedAnimation(
          parent: controller,
          curve: Curves.easeOutCubic,
          reverseCurve: Curves.easeInCubic,
        );
        
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(1.0, 0.0), // Slide in from right
            end: Offset.zero,
          ).animate(animation),
          child: FadeTransition(
            opacity: animation,
            child: child,
          ),
        );
      },
    );
  }
  
  /// Create optimized orientation change transition
  static Widget createOrientationTransition({
    required Widget child,
    required Orientation orientation,
    required TickerProvider vsync,
    String? key,
  }) {
    final animationKey = key ?? 'orientation_${orientation.name}';
    
    // Track orientation change performance
    _performanceUtils.startComponentTracking('orientationTransition');
    
    return AnimatedSwitcher(
      duration: _fastTransition,
      transitionBuilder: (child, animation) {
        // Use different transitions based on device type
        final isTablet = DesignSystem.isTablet(child.key?.currentContext ?? 
                                              WidgetsBinding.instance.rootElement!);
        
        if (isTablet) {
          // Tablets: Smooth fade with scale for better visual feedback
          return FadeTransition(
            opacity: animation,
            child: ScaleTransition(
              scale: Tween<double>(
                begin: 0.95,
                end: 1.0,
              ).animate(CurvedAnimation(
                parent: animation,
                curve: Curves.easeOutCubic,
              )),
              child: child,
            ),
          );
        } else {
          // Mobile: Simple fade for performance
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        }
      },
      layoutBuilder: (currentChild, previousChildren) {
        // Optimize layout during transition
        return Stack(
          alignment: Alignment.center,
          children: [
            ...previousChildren,
            if (currentChild != null) currentChild,
          ],
        );
      },
      child: KeyedSubtree(
        key: ValueKey(orientation),
        child: child,
      ),
    );
  }
  
  /// Create optimized PageController with performance monitoring
  static PageController createOptimizedPageController({
    int initialPage = 0,
    bool keepPage = true,
    double viewportFraction = 1.0,
  }) {
    return PageController(
      initialPage: initialPage,
      keepPage: keepPage,
      viewportFraction: viewportFraction,
    );
  }
  
  /// Optimize PageController animation with performance tracking
  static Future<void> animateToPageOptimized(
    PageController controller,
    int page, {
    Duration? duration,
    Curve curve = Curves.easeInOut,
    String? trackingKey,
  }) async {
    final animationDuration = duration ?? _normalTransition;
    final key = trackingKey ?? 'pageAnimation_$page';
    
    // Track page animation performance
    _performanceUtils.startComponentTracking(key);
    
    try {
      await controller.animateToPage(
        page,
        duration: animationDuration,
        curve: curve,
      );
    } finally {
      // Complete tracking
      SchedulerBinding.instance.addPostFrameCallback((_) {
        _performanceUtils.stopComponentTracking(key);
      });
    }
  }
  
  /// Create responsive layout transition with performance optimization
  static Widget createResponsiveLayoutTransition({
    required Widget child,
    required bool isTabletLandscape,
    required TickerProvider vsync,
    String? key,
  }) {
    final animationKey = key ?? 'responsiveLayout';
    
    // Track responsive layout performance
    _performanceUtils.startComponentTracking('responsiveLayoutTransition');
    
    return AnimatedSwitcher(
      duration: _fastTransition,
      transitionBuilder: (child, animation) {
        return FadeTransition(
          opacity: CurvedAnimation(
            parent: animation,
            curve: Curves.easeOutCubic,
          ),
          child: child,
        );
      },
      layoutBuilder: (currentChild, previousChildren) {
        // Optimize layout calculation during transition
        return RepaintBoundary(
          child: Stack(
            alignment: Alignment.topLeft,
            children: [
              ...previousChildren,
              if (currentChild != null) currentChild,
            ],
          ),
        );
      },
      child: KeyedSubtree(
        key: ValueKey(isTabletLandscape),
        child: child,
      ),
    );
  }
  
  /// Get or create animation controller with caching
  static AnimationController _getOrCreateController(
    String key,
    Duration duration,
    TickerProvider vsync,
  ) {
    if (_controllers.containsKey(key)) {
      final controller = _controllers[key]!;
      if (controller.duration != duration) {
        controller.duration = duration;
      }
      return controller;
    }
    
    final controller = AnimationController(
      duration: duration,
      vsync: vsync,
    );
    
    // Add disposal listener
    controller.addStatusListener((status) {
      if (status == AnimationStatus.completed || 
          status == AnimationStatus.dismissed) {
        SchedulerBinding.instance.addPostFrameCallback((_) {
          _performanceUtils.stopComponentTracking('${key}Animation');
        });
      }
    });
    
    _controllers[key] = controller;
    return controller;
  }
  
  /// Dispose animation controller
  static void disposeController(String key) {
    final controller = _controllers.remove(key);
    controller?.dispose();
  }
  
  /// Dispose all animation controllers
  static void disposeAllControllers() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    _controllers.clear();
  }
  
  /// Get optimal transition duration based on device performance
  static Duration getOptimalTransitionDuration(BuildContext context) {
    // Adjust duration based on device capabilities
    final isTablet = DesignSystem.isTablet(context);
    final screenWidth = ResponsiveSystem.getScreenWidth(context);
    
    if (isTablet && screenWidth > 1024) {
      // High-end tablets: Use normal transitions
      return _normalTransition;
    } else if (isTablet) {
      // Standard tablets: Use fast transitions
      return _fastTransition;
    } else {
      // Mobile devices: Use fastest transitions for performance
      return _fastTransition;
    }
  }
}
