import 'package:flutter/foundation.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/responsive_system.dart';

/// Tablet-specific performance configuration
///
/// This class provides tablet-optimized performance thresholds and configuration
/// parameters that account for the increased complexity and hardware capabilities
/// of tablet devices.
class TabletPerformanceConfig {
  /// Get tablet-specific FPS target based on device capabilities
  static double getTabletFPSTarget() {
    // Tablets typically have more powerful hardware but also more complex layouts
    if (kDebugMode) return 55.0; // Slightly lower for development debugging
    if (kProfileMode) return 50.0; // Moderate for performance testing
    return 45.0; // Practical target for production tablets
  }

  /// Get mobile FPS target for comparison
  static double getMobileFPSTarget() {
    if (kDebugMode) return 60.0; // Standard mobile target
    if (kProfileMode) return 55.0; // Moderate for testing
    return 50.0; // Production mobile target
  }

  /// Get form factor-aware FPS target
  static double getFPSTarget(bool isTablet) {
    return isTablet ? getTabletFPSTarget() : getMobileFPSTarget();
  }

  /// Get tablet-specific jank rate threshold
  static double getTabletJankRateThreshold() {
    // More lenient jank rate for tablets due to layout complexity
    if (kDebugMode) return 25.0; // Development threshold
    if (kProfileMode) return 30.0; // Testing threshold
    return 35.0; // Production threshold for tablets
  }

  /// Get mobile jank rate threshold for comparison
  static double getMobileJankRateThreshold() {
    if (kDebugMode) return 15.0; // Strict mobile threshold
    if (kProfileMode) return 20.0; // Testing threshold
    return 25.0; // Production mobile threshold
  }

  /// Get form factor-aware jank rate threshold
  static double getJankRateThreshold(bool isTablet) {
    return isTablet ? getTabletJankRateThreshold() : getMobileJankRateThreshold();
  }

  /// Get tablet-specific memory pressure threshold
  static double getTabletMemoryThreshold() {
    // Tablets typically have more RAM, so higher threshold
    if (kDebugMode) return 75.0; // 75% for development
    if (kProfileMode) return 80.0; // 80% for testing
    return 85.0; // 85% for production tablets
  }

  /// Get mobile memory pressure threshold
  static double getMobileMemoryThreshold() {
    if (kDebugMode) return 70.0; // Conservative for mobile
    if (kProfileMode) return 75.0; // Testing threshold
    return 80.0; // Production mobile threshold
  }

  /// Get form factor-aware memory threshold
  static double getMemoryThreshold(bool isTablet) {
    return isTablet ? getTabletMemoryThreshold() : getMobileMemoryThreshold();
  }

  /// Get tablet-specific startup time target
  static Duration getTabletStartupTarget() {
    // Tablets can afford slightly longer startup due to complexity
    if (kDebugMode) return const Duration(milliseconds: 800); // Development target
    if (kProfileMode) return const Duration(milliseconds: 700); // Testing target
    return const Duration(milliseconds: 600); // Production target (vs 555ms mobile)
  }

  /// Get mobile startup time target
  static Duration getMobileStartupTarget() {
    if (kDebugMode) return const Duration(milliseconds: 600); // Mobile development
    if (kProfileMode) return const Duration(milliseconds: 555); // Mobile testing
    return const Duration(milliseconds: 500); // Mobile production
  }

  /// Get form factor-aware startup target
  static Duration getStartupTarget(bool isTablet) {
    return isTablet ? getTabletStartupTarget() : getMobileStartupTarget();
  }

  /// Get tablet-specific component render time thresholds
  static Map<String, Duration> getTabletComponentThresholds() {
    return {
      // NavigationRail - more complex on tablets
      'navigationRail': const Duration(milliseconds: 22),
      'sideMenu': const Duration(milliseconds: 20),
      
      // Master-detail layout - tablet-specific
      'masterDetailLayout': const Duration(milliseconds: 28),
      'profilePane': const Duration(milliseconds: 25),
      
      // Search and interaction components
      'searchBar': const Duration(milliseconds: 18),
      'contextMenu': const Duration(milliseconds: 20),
      
      // Content rendering - larger screens
      'webView': const Duration(milliseconds: 35),
      'epubRenderer': const Duration(milliseconds: 40),
      
      // List and grid components
      'bookList': const Duration(milliseconds: 25),
      'tabletGrid': const Duration(milliseconds: 30),
      
      // Settings and configuration
      'settingsPanel': const Duration(milliseconds: 22),
      'preferencesPage': const Duration(milliseconds: 25),
      
      // Layout transitions
      'layoutTransition': const Duration(milliseconds: 100),
      'orientationChange': const Duration(milliseconds: 150),
    };
  }

  /// Get mobile component render time thresholds
  static Map<String, Duration> getMobileComponentThresholds() {
    return {
      // Standard mobile thresholds
      'navigationRail': const Duration(milliseconds: 16),
      'sideMenu': const Duration(milliseconds: 16),
      'masterDetailLayout': const Duration(milliseconds: 16),
      'profilePane': const Duration(milliseconds: 16),
      'searchBar': const Duration(milliseconds: 16),
      'contextMenu': const Duration(milliseconds: 16),
      'webView': const Duration(milliseconds: 20),
      'epubRenderer': const Duration(milliseconds: 25),
      'bookList': const Duration(milliseconds: 16),
      'tabletGrid': const Duration(milliseconds: 16),
      'settingsPanel': const Duration(milliseconds: 16),
      'preferencesPage': const Duration(milliseconds: 16),
      'layoutTransition': const Duration(milliseconds: 50),
      'orientationChange': const Duration(milliseconds: 100),
    };
  }

  /// Get form factor-aware component thresholds
  static Map<String, Duration> getComponentThresholds(bool isTablet) {
    return isTablet ? getTabletComponentThresholds() : getMobileComponentThresholds();
  }

  /// Get tablet-specific layout complexity thresholds
  static Map<String, int> getTabletComplexityThresholds() {
    return {
      'maxWidgetTreeDepth': 25, // Deeper trees acceptable on tablets
      'maxActiveProviders': 15, // More providers for complex layouts
      'maxSimultaneousAnimations': 4, // More animations acceptable
      'maxVisibleComponents': 12, // More components visible on larger screens
    };
  }

  /// Get mobile layout complexity thresholds
  static Map<String, int> getMobileComplexityThresholds() {
    return {
      'maxWidgetTreeDepth': 20, // Shallower trees for mobile
      'maxActiveProviders': 10, // Fewer providers
      'maxSimultaneousAnimations': 3, // Fewer animations
      'maxVisibleComponents': 8, // Fewer visible components
    };
  }

  /// Get form factor-aware complexity thresholds
  static Map<String, int> getComplexityThresholds(bool isTablet) {
    return isTablet ? getTabletComplexityThresholds() : getMobileComplexityThresholds();
  }

  /// Get optimization cycle interval based on form factor
  static Duration getOptimizationInterval(bool isTablet) {
    // Tablets may need more frequent optimization due to complexity
    if (isTablet) {
      if (kDebugMode) return const Duration(seconds: 2); // More frequent for tablet debugging
      if (kProfileMode) return const Duration(seconds: 4); // Moderate for testing
      return const Duration(seconds: 8); // Production tablet interval
    } else {
      if (kDebugMode) return const Duration(seconds: 3); // Standard mobile debugging
      if (kProfileMode) return const Duration(seconds: 5); // Mobile testing
      return const Duration(seconds: 10); // Mobile production
    }
  }

  /// Get performance score adjustment for tablets
  static double getTabletPerformanceAdjustment() {
    // Tablets get a small performance bonus due to increased complexity
    return 5.0; // 5 point bonus for tablet complexity expectations
  }

  /// Get comprehensive configuration summary
  static Map<String, dynamic> getConfigurationSummary(bool isTablet, bool isLandscape) {
    return {
      'formFactor': {
        'isTablet': isTablet,
        'isLandscape': isLandscape,
        'deviceType': isTablet ? 'tablet' : 'mobile',
        'orientation': isLandscape ? 'landscape' : 'portrait',
      },
      'performanceTargets': {
        'fpsTarget': getFPSTarget(isTablet),
        'jankRateThreshold': getJankRateThreshold(isTablet),
        'memoryThreshold': getMemoryThreshold(isTablet),
        'startupTarget': getStartupTarget(isTablet).inMilliseconds,
      },
      'optimizationSettings': {
        'optimizationInterval': getOptimizationInterval(isTablet).inSeconds,
        'performanceAdjustment': isTablet ? getTabletPerformanceAdjustment() : 0.0,
      },
      'complexityLimits': getComplexityThresholds(isTablet),
      'componentThresholds': getComponentThresholds(isTablet).map(
        (key, value) => MapEntry(key, value.inMilliseconds),
      ),
      'buildMode': kDebugMode ? 'debug' : (kProfileMode ? 'profile' : 'release'),
    };
  }

  /// Validate configuration for specific device
  static bool validateConfiguration(bool isTablet, bool isLandscape) {
    try {
      // Validate FPS targets
      final fpsTarget = getFPSTarget(isTablet);
      if (fpsTarget < 30.0 || fpsTarget > 120.0) return false;

      // Validate jank thresholds
      final jankThreshold = getJankRateThreshold(isTablet);
      if (jankThreshold < 10.0 || jankThreshold > 50.0) return false;

      // Validate memory thresholds
      final memoryThreshold = getMemoryThreshold(isTablet);
      if (memoryThreshold < 50.0 || memoryThreshold > 95.0) return false;

      // Validate startup targets
      final startupTarget = getStartupTarget(isTablet);
      if (startupTarget.inMilliseconds < 200 || startupTarget.inMilliseconds > 2000) return false;

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('TabletPerformanceConfig validation failed: $e');
      }
      return false;
    }
  }
}
