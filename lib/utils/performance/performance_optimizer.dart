import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/performance/startup_optimizer.dart';
import 'package:dasso_reader/utils/performance/widget_optimizer.dart';
import 'package:dasso_reader/utils/performance/jank_mitigation.dart';
import 'package:dasso_reader/utils/performance/frame_rate_monitor.dart';
import 'package:dasso_reader/utils/performance/memory_usage_monitor.dart';
import 'package:dasso_reader/utils/performance/tablet_performance_utils.dart';

/// Production-ready performance configuration
class ProductionPerformanceConfig {
  /// Jank rate threshold based on build mode
  static double get jankRateThreshold {
    if (kDebugMode) return 15.0; // Aggressive for development debugging
    if (kProfileMode) return 20.0; // Moderate for performance testing
    return 25.0; // Optimized for production excellence
  }

  /// Optimization cycle interval based on build mode
  static Duration get optimizationInterval {
    if (kDebugMode) return const Duration(seconds: 3); // Frequent for debugging
    if (kProfileMode) return const Duration(seconds: 5); // Moderate for testing
    return const Duration(seconds: 10); // Efficient for production
  }

  /// Frame rate threshold for low FPS detection
  static double get lowFPSThreshold {
    if (kDebugMode) return 45.0; // Strict for development
    if (kProfileMode) return 40.0; // Moderate for testing
    return 35.0; // Practical for production
  }

  /// Memory pressure threshold
  static double get memoryPressureThreshold {
    if (kDebugMode) return 70.0; // Early warning for development
    if (kProfileMode) return 80.0; // Balanced for testing
    return 85.0; // Conservative for production
  }

  /// Enable detailed logging (disabled by default for cleaner development)
  static bool get enableDetailedLogging {
    return false; // Disabled for cleaner development experience
  }

  /// Enable verbose logging for performance debugging
  static bool get enableVerboseLogging {
    return !kReleaseMode; // Only in debug and profile modes
  }

  /// Get current configuration summary
  static Map<String, dynamic> getCurrentConfig() {
    return {
      'buildMode':
          kDebugMode ? 'debug' : (kProfileMode ? 'profile' : 'release'),
      'jankRateThreshold': jankRateThreshold,
      'optimizationInterval': optimizationInterval.inSeconds,
      'lowFPSThreshold': lowFPSThreshold,
      'memoryPressureThreshold': memoryPressureThreshold,
      'detailedLogging': enableDetailedLogging,
    };
  }
}

/// Comprehensive performance optimization system for Dasso Reader
class PerformanceOptimizer {
  static final PerformanceOptimizer _instance =
      PerformanceOptimizer._internal();
  factory PerformanceOptimizer() => _instance;
  PerformanceOptimizer._internal();

  bool _isInitialized = false;
  Timer? _optimizationTimer;

  // Performance monitoring components
  late final TabletPerformanceMonitor _frameRateMonitor;
  late final MemoryUsageMonitor _memoryMonitor;
  late final JankMitigationSystem _jankMitigation;
  late final StartupOptimizer _startupOptimizer;
  late final WidgetOptimizer _widgetOptimizer;

  /// Initialize the performance optimization system
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Log in both debug and profile modes
    AnxLog.info(
      '🚀 PerformanceOptimizer: Initializing comprehensive optimization system',
    );

    try {
      // Initialize monitoring components
      _frameRateMonitor = TabletPerformanceMonitor();
      _memoryMonitor = MemoryUsageMonitor();
      _jankMitigation = JankMitigationSystem();
      _startupOptimizer = StartupOptimizer();
      _widgetOptimizer = WidgetOptimizer();

      // Initialize all components
      await _frameRateMonitor.initialize(FrameRateConfig.defaultConfig());
      await _memoryMonitor.initialize(MemoryConfig.defaultConfig());
      await _jankMitigation.initialize();
      _widgetOptimizer.initialize();

      // Initialize tablet performance utilities
      TabletPerformanceUtils.instance.initialize(_frameRateMonitor);

      // Start monitoring
      _frameRateMonitor.startMonitoring();
      _memoryMonitor.startMonitoring();

      // Setup optimization loop
      _setupOptimizationLoop();

      _isInitialized = true;

      // Log in both debug and profile modes
      AnxLog.info('🚀 PerformanceOptimizer: Initialization complete');
    } catch (e) {
      AnxLog.severe('PerformanceOptimizer initialization failed: $e');
      rethrow;
    }
  }

  /// Setup the main optimization loop with production-ready intervals
  void _setupOptimizationLoop() {
    final interval = ProductionPerformanceConfig.optimizationInterval;
    _optimizationTimer = Timer.periodic(interval, (_) {
      _performOptimizationCycle();
    });

    // Log configuration in debug mode
    if (ProductionPerformanceConfig.enableDetailedLogging) {
      final config = ProductionPerformanceConfig.getCurrentConfig();
      AnxLog.info('🔧 Performance Config: ${config['buildMode']} mode');
      AnxLog.info('  Jank Threshold: ${config['jankRateThreshold']}%');
      AnxLog.info(
        '  Optimization Interval: ${config['optimizationInterval']}s',
      );
      AnxLog.info('  Low FPS Threshold: ${config['lowFPSThreshold']} FPS');
    }
  }

  /// Perform a complete optimization cycle
  void _performOptimizationCycle() {
    if (!_isInitialized) return;

    try {
      // Get current performance metrics
      final frameMetrics = _frameRateMonitor.getMetrics();
      final memoryMetrics = _memoryMonitor.getMetrics();

      // Analyze performance issues
      final issues = _analyzePerformanceIssues(frameMetrics, memoryMetrics);

      // Apply optimizations based on detected issues
      _applyOptimizations(issues);

      // Log optimization summary only if verbose logging is enabled
      if (issues.isNotEmpty &&
          ProductionPerformanceConfig.enableVerboseLogging) {
        _logOptimizationSummary(issues, frameMetrics, memoryMetrics);
      }
    } catch (e) {
      if (kDebugMode) {
        AnxLog.warning('Optimization cycle error: $e');
      }
    }
  }

  /// Analyze current performance metrics to identify issues
  List<PerformanceIssue> _analyzePerformanceIssues(
    FrameRateMetrics frameMetrics,
    MemoryMetrics memoryMetrics,
  ) {
    final issues = <PerformanceIssue>[];

    // Check frame rate issues - production-ready thresholds
    final jankThreshold = ProductionPerformanceConfig.jankRateThreshold;
    if (frameMetrics.jankRate > jankThreshold) {
      issues.add(
        PerformanceIssue(
          type: IssueType.highJankRate,
          severity: _getJankSeverity(frameMetrics.jankRate),
          description:
              'High jank rate: ${frameMetrics.jankRate.toStringAsFixed(1)}% (threshold: ${jankThreshold.toStringAsFixed(1)}%)',
          metrics: {
            'jankRate': frameMetrics.jankRate,
            'jankCount': frameMetrics.jankCount,
            'threshold': jankThreshold,
          },
        ),
      );
    }

    final fpsThreshold = ProductionPerformanceConfig.lowFPSThreshold;
    if (frameMetrics.currentFPS < fpsThreshold) {
      issues.add(
        PerformanceIssue(
          type: IssueType.lowFrameRate,
          severity: IssueSeverity.moderate,
          description:
              'Low frame rate: ${frameMetrics.currentFPS.toStringAsFixed(1)} FPS (threshold: ${fpsThreshold.toStringAsFixed(1)} FPS)',
          metrics: {
            'currentFPS': frameMetrics.currentFPS,
            'targetFPS': frameMetrics.targetFPS,
            'threshold': fpsThreshold,
          },
        ),
      );
    }

    // Check memory issues
    if (memoryMetrics.isMemoryPressure) {
      issues.add(
        PerformanceIssue(
          type: IssueType.memoryPressure,
          severity: IssueSeverity.high,
          description:
              'Memory pressure detected: ${memoryMetrics.usagePercent.toStringAsFixed(1)}%',
          metrics: {
            'usagePercent': memoryMetrics.usagePercent,
            'currentUsage': memoryMetrics.currentUsageMB,
          },
        ),
      );
    }

    if (memoryMetrics.memorySpikes.isNotEmpty) {
      final recentSpikes = memoryMetrics.memorySpikes
          .where(
            (spike) => DateTime.now().difference(spike.timestamp).inMinutes < 5,
          )
          .length;

      if (recentSpikes > 2) {
        issues.add(
          PerformanceIssue(
            type: IssueType.memorySpikes,
            severity: IssueSeverity.moderate,
            description: 'Frequent memory spikes: $recentSpikes in 5 minutes',
            metrics: {
              'recentSpikes': recentSpikes,
              'totalSpikes': memoryMetrics.memorySpikes.length,
            },
          ),
        );
      }
    }

    return issues;
  }

  /// Apply optimizations based on detected issues
  void _applyOptimizations(List<PerformanceIssue> issues) {
    for (final issue in issues) {
      switch (issue.type) {
        case IssueType.highJankRate:
          // Apply aggressive jank mitigation for high jank rates
          _jankMitigation.mitigateJank(
            JankType.excessiveRebuilds,
            issue.metrics,
          );

          // Additional optimization for high jank
          if (issue.severity == IssueSeverity.critical) {
            _widgetOptimizer.enableAggressiveOptimization();
            AnxLog.warning(
              '🚨 CRITICAL JANK DETECTED (${issue.metrics['jankRate']?.toStringAsFixed(1)}%), enabling aggressive optimization',
            );
          } else if (issue.severity == IssueSeverity.high) {
            AnxLog.warning(
              '⚠️ HIGH JANK DETECTED (${issue.metrics['jankRate']?.toStringAsFixed(1)}%), applying optimizations',
            );
          }
          break;
        case IssueType.lowFrameRate:
          _optimizeFrameRate(issue);
          break;
        case IssueType.memoryPressure:
          _memoryMonitor.forceCleanup();
          // Also trigger widget optimization to reduce memory usage
          _widgetOptimizer.optimizeMemoryUsage();
          break;
        case IssueType.memorySpikes:
          _jankMitigation.mitigateJank(JankType.memorySpike, issue.metrics);
          break;
        case IssueType.slowStartup:
          _jankMitigation.mitigateJank(JankType.startup, issue.metrics);
          break;
        case IssueType.excessiveRebuilds:
          _jankMitigation.mitigateJank(
            JankType.excessiveRebuilds,
            issue.metrics,
          );
          _widgetOptimizer.reduceRebuilds();
          break;
      }
    }
  }

  /// Optimize frame rate based on detected issues
  void _optimizeFrameRate(PerformanceIssue issue) {
    // Schedule frame rate optimization on next frame
    SchedulerBinding.instance.addPostFrameCallback((_) {
      // This could trigger widget optimization, reduce animations, etc.
      if (kDebugMode) {
        AnxLog.info('🎯 Applying frame rate optimization');
      }
    });
  }

  /// Get jank severity based on jank rate with production-ready thresholds
  IssueSeverity _getJankSeverity(double jankRate) {
    final baseThreshold = ProductionPerformanceConfig.jankRateThreshold;

    // Dynamic thresholds based on configuration
    if (jankRate > baseThreshold * 2.0) {
      return IssueSeverity.critical; // 50% in production, 30% in debug
    }
    if (jankRate > baseThreshold * 1.5) {
      return IssueSeverity.high; // 37.5% in production, 22.5% in debug
    }
    if (jankRate > baseThreshold) {
      return IssueSeverity.moderate; // 25% in production, 15% in debug
    }
    return IssueSeverity.low;
  }

  /// Log optimization summary
  void _logOptimizationSummary(
    List<PerformanceIssue> issues,
    FrameRateMetrics frameMetrics,
    MemoryMetrics memoryMetrics,
  ) {
    final criticalIssues =
        issues.where((i) => i.severity == IssueSeverity.critical).length;
    final highIssues =
        issues.where((i) => i.severity == IssueSeverity.high).length;

    AnxLog.info('📊 Performance Optimization Summary:');
    AnxLog.info(
      '  Issues detected: ${issues.length} (Critical: $criticalIssues, High: $highIssues)',
    );
    AnxLog.info(
      '  Current FPS: ${frameMetrics.currentFPS.toStringAsFixed(1)} (Jank: ${frameMetrics.jankRate.toStringAsFixed(1)}%)',
    );
    AnxLog.info(
      '  Memory: ${memoryMetrics.currentUsageMB}MB (${memoryMetrics.usagePercent.toStringAsFixed(1)}%)',
    );

    for (final issue in issues.take(3)) {
      // Log top 3 issues
      AnxLog.info('  🔍 ${issue.description}');
    }
  }

  /// Enhanced diagnostic method for troubleshooting performance system
  Map<String, dynamic> getDiagnosticInfo() {
    final diagnostics = <String, dynamic>{
      'systemStatus': {
        'isInitialized': _isInitialized,
        'optimizationTimerActive': _optimizationTimer?.isActive ?? false,
        'lastOptimizationCycle': DateTime.now().toIso8601String(),
      },
      'componentStatus': {
        'frameRateMonitor': _frameRateMonitor.currentFPS,
        'memoryMonitor': _memoryMonitor.getMetrics().currentUsageMB,
        'jankMitigation': _jankMitigation.toString(),
        'widgetOptimizer': _widgetOptimizer.toString(),
      },
      'thresholds': {
        'jankRateThreshold': 15.0,
        'lowFPSThreshold': 45.0,
        'optimizationCycleInterval': '3 seconds',
      },
      'recentActivity': _getRecentActivity(),
    };

    // Log diagnostic info for debugging
    AnxLog.info('🔧 Performance System Diagnostics:');
    AnxLog.info('  System Initialized: $_isInitialized');
    AnxLog.info(
      '  Optimization Timer Active: ${_optimizationTimer?.isActive ?? false}',
    );
    AnxLog.info(
      '  Current FPS: ${_frameRateMonitor.currentFPS.toStringAsFixed(1)}',
    );
    AnxLog.info(
      '  Memory Usage: ${_memoryMonitor.getMetrics().currentUsageMB}MB',
    );

    return diagnostics;
  }

  /// Get recent optimization activity
  Map<String, dynamic> _getRecentActivity() {
    final frameMetrics = _frameRateMonitor.getMetrics();
    final memoryMetrics = _memoryMonitor.getMetrics();
    final issues = _analyzePerformanceIssues(frameMetrics, memoryMetrics);

    return {
      'currentIssues': issues.length,
      'jankEventsLast5Min': frameMetrics.jankEvents
          .where(
            (event) => DateTime.now().difference(event.timestamp).inMinutes < 5,
          )
          .length,
      'memorySpikesLast5Min': memoryMetrics.memorySpikes
          .where(
            (spike) => DateTime.now().difference(spike.timestamp).inMinutes < 5,
          )
          .length,
      'performanceScore': _calculateOverallScore(frameMetrics, memoryMetrics),
    };
  }

  /// Get comprehensive performance report
  PerformanceReport getPerformanceReport() {
    if (!_isInitialized) {
      return PerformanceReport.empty();
    }

    final frameMetrics = _frameRateMonitor.getMetrics();
    final memoryMetrics = _memoryMonitor.getMetrics();
    final issues = _analyzePerformanceIssues(frameMetrics, memoryMetrics);

    return PerformanceReport(
      frameMetrics: frameMetrics,
      memoryMetrics: memoryMetrics,
      issues: issues,
      overallScore: _calculateOverallScore(frameMetrics, memoryMetrics),
      timestamp: DateTime.now(),
    );
  }

  /// Calculate overall performance score (0-100)
  double _calculateOverallScore(
    FrameRateMetrics frameMetrics,
    MemoryMetrics memoryMetrics,
  ) {
    final frameScore = frameMetrics.performanceScore;
    final memoryScore = memoryMetrics.performanceScore;

    // Weighted average: 60% frame performance, 40% memory performance
    return (frameScore * 0.6 + memoryScore * 0.4).clamp(0.0, 100.0);
  }

  /// Dispose all resources
  void dispose() {
    _optimizationTimer?.cancel();

    if (_isInitialized) {
      _frameRateMonitor.dispose();
      _memoryMonitor.dispose();
      _jankMitigation.dispose();
      _startupOptimizer.reset();
      _widgetOptimizer.dispose();
    }

    _isInitialized = false;

    if (kDebugMode) {
      AnxLog.info('🚀 PerformanceOptimizer: Disposed');
    }
  }
}

/// Represents a performance issue
class PerformanceIssue {
  const PerformanceIssue({
    required this.type,
    required this.severity,
    required this.description,
    required this.metrics,
  });

  final IssueType type;
  final IssueSeverity severity;
  final String description;
  final Map<String, dynamic> metrics;
}

/// Types of performance issues
enum IssueType {
  highJankRate,
  lowFrameRate,
  memoryPressure,
  memorySpikes,
  slowStartup,
  excessiveRebuilds,
}

/// Severity levels for performance issues
enum IssueSeverity {
  low,
  moderate,
  high,
  critical,
}

/// Comprehensive performance report
class PerformanceReport {
  const PerformanceReport({
    required this.frameMetrics,
    required this.memoryMetrics,
    required this.issues,
    required this.overallScore,
    required this.timestamp,
  });

  final FrameRateMetrics frameMetrics;
  final MemoryMetrics memoryMetrics;
  final List<PerformanceIssue> issues;
  final double overallScore;
  final DateTime timestamp;

  factory PerformanceReport.empty() {
    return PerformanceReport(
      frameMetrics: const FrameRateMetrics(
        currentFPS: 0,
        averageFPS: 0,
        targetFPS: 60,
        jankCount: 0,
        totalFrames: 0,
        jankEvents: [],
        performanceScore: 0,
      ),
      memoryMetrics: const MemoryMetrics(
        currentUsageMB: 0,
        peakUsageMB: 0,
        deviceLimitMB: 0,
        memoryHistory: [],
        memorySpikes: [],
        largeObjectCount: 0,
        isMemoryPressure: false,
        performanceScore: 0,
      ),
      issues: [],
      overallScore: 0,
      timestamp: DateTime.now(),
    );
  }

  bool get hasIssues => issues.isNotEmpty;
  bool get hasCriticalIssues =>
      issues.any((i) => i.severity == IssueSeverity.critical);
  bool get isPerformanceAcceptable => overallScore >= 70.0;
}
