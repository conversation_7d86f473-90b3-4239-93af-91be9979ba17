import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/performance/frame_rate_monitor.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/responsive_system.dart';

/// NavigationRail performance baseline data class
class NavigationRailBaseline {
  final String railId;
  final int destinationCount;
  final bool hasSearchBar;
  final bool isExtended;
  final bool isTablet;
  final bool isLandscape;
  final DateTime createdAt;

  final List<Duration> renderTimes = [];
  final List<int> frameDropCounts = [];
  final List<double> animationSmoothness = [];
  final Map<String, dynamic> additionalMetrics = {};

  int totalRebuilds = 0;

  NavigationRailBaseline({
    required this.railId,
    required this.destinationCount,
    required this.hasSearchBar,
    required this.isExtended,
    required this.isTablet,
    required this.isLandscape,
    required this.createdAt,
  });

  /// Add render time measurement
  void addRenderTime(Duration renderTime) {
    renderTimes.add(renderTime);
    // Keep only recent measurements (last 50)
    if (renderTimes.length > 50) {
      renderTimes.removeAt(0);
    }
  }

  /// Add frame drop count
  void addFrameDrops(int frameDrops) {
    frameDropCounts.add(frameDrops);
    // Keep only recent measurements (last 50)
    if (frameDropCounts.length > 50) {
      frameDropCounts.removeAt(0);
    }
  }

  /// Add animation smoothness measurement
  void addAnimationSmoothness(double smoothness) {
    animationSmoothness.add(smoothness);
    // Keep only recent measurements (last 50)
    if (animationSmoothness.length > 50) {
      animationSmoothness.removeAt(0);
    }
  }

  /// Check if baseline has enough data for analysis
  bool get isComplete {
    return renderTimes.length >= 5 && totalRebuilds > 0;
  }

  /// Get comprehensive performance analysis
  Map<String, dynamic> getPerformanceAnalysis() {
    final analysis = <String, dynamic>{};

    // Render time analysis
    if (renderTimes.isNotEmpty) {
      final avgRenderTime = renderTimes
              .map((d) => d.inMicroseconds / 1000)
              .reduce((a, b) => a + b) /
          renderTimes.length;
      final maxRenderTime = renderTimes
          .map((d) => d.inMicroseconds / 1000)
          .reduce((a, b) => a > b ? a : b);

      analysis['averageRenderTime'] = avgRenderTime;
      analysis['maxRenderTime'] = maxRenderTime;
      analysis['renderTimeSamples'] = renderTimes.length;
    }

    // Frame drop analysis
    if (frameDropCounts.isNotEmpty) {
      final totalFrameDrops = frameDropCounts.reduce((a, b) => a + b);
      final avgFrameDrops = totalFrameDrops / frameDropCounts.length;
      final frameDropRate = frameDropCounts.where((count) => count > 0).length /
          frameDropCounts.length *
          100;

      analysis['totalFrameDrops'] = totalFrameDrops;
      analysis['averageFrameDrops'] = avgFrameDrops;
      analysis['frameDropRate'] = frameDropRate;
    }

    // Animation smoothness analysis
    if (animationSmoothness.isNotEmpty) {
      final avgSmoothness = animationSmoothness.reduce((a, b) => a + b) /
          animationSmoothness.length;

      analysis['averageAnimationSmoothness'] = avgSmoothness;
      analysis['animationSamples'] = animationSmoothness.length;
    }

    // Rebuild analysis
    analysis['totalRebuilds'] = totalRebuilds;

    // Performance score calculation (0-100)
    double score = 100.0;

    if (analysis.containsKey('averageRenderTime')) {
      final renderTime = analysis['averageRenderTime'] as double;
      if (renderTime > 16) {
        score -= (renderTime - 16) * 2; // Penalty for slow renders
      }
    }

    if (analysis.containsKey('frameDropRate')) {
      final dropRate = analysis['frameDropRate'] as double;
      score -= dropRate * 0.5; // Penalty for frame drops
    }

    if (totalRebuilds > 20) {
      score -= (totalRebuilds - 20) * 0.5; // Penalty for excessive rebuilds
    }

    analysis['performanceScore'] = score.clamp(0.0, 100.0);

    // Generate recommendations
    final recommendations = <String>[];

    if (analysis.containsKey('averageRenderTime') &&
        (analysis['averageRenderTime'] as double) > 16) {
      recommendations.add('Render time exceeds 16ms - consider optimization');
    }

    if (analysis.containsKey('frameDropRate') &&
        (analysis['frameDropRate'] as double) > 5) {
      recommendations
          .add('High frame drop rate - investigate performance bottlenecks');
    }

    if (totalRebuilds > 30) {
      recommendations
          .add('Excessive rebuilds detected - optimize state management');
    }

    if (destinationCount > 8) {
      recommendations
          .add('Many destinations - consider grouping or lazy loading');
    }

    analysis['recommendations'] = recommendations;

    return analysis;
  }
}

/// Tablet-specific performance metrics collection utilities
///
/// Provides specialized tracking for:
/// - NavigationRail render time monitoring
/// - Master-detail layout performance analysis
/// - WebView frame drop detection
/// - Complex widget tree analysis
class TabletPerformanceUtils {
  static TabletPerformanceUtils? _instance;
  static TabletPerformanceUtils get instance =>
      _instance ??= TabletPerformanceUtils._();
  TabletPerformanceUtils._();

  TabletPerformanceMonitor? _monitor;
  final Map<String, Stopwatch> _componentStopwatches = {};
  final Map<String, List<Duration>> _renderTimeHistory = {};

  /// Initialize tablet performance utilities
  void initialize(TabletPerformanceMonitor monitor) {
    _monitor = monitor;
    _initializeComponentTracking();

    if (kDebugMode) {
      AnxLog.info(
        '📊 TabletPerformanceUtils: Initialized with component tracking',
      );
    }
  }

  /// Initialize component tracking
  void _initializeComponentTracking() {
    final components = [
      'navigationRail',
      'masterDetailLayout',
      'sideMenu',
      'searchBar',
      'profilePane',
      'webView',
      'bookList',
      'settingsPanel',
    ];

    for (final component in components) {
      _componentStopwatches[component] = Stopwatch();
      _renderTimeHistory[component] = <Duration>[];
    }
  }

  /// Start tracking component render time
  void startComponentTracking(String component) {
    final stopwatch = _componentStopwatches[component];
    if (stopwatch != null) {
      stopwatch.reset();
      stopwatch.start();
    }
  }

  /// Stop tracking component render time and record metrics
  void stopComponentTracking(String component) {
    final stopwatch = _componentStopwatches[component];
    if (stopwatch != null && stopwatch.isRunning) {
      stopwatch.stop();
      final renderTime = stopwatch.elapsed;

      // Record in history
      final history = _renderTimeHistory[component];
      if (history != null) {
        history.add(renderTime);

        // Maintain history size
        while (history.length > 20) {
          history.removeAt(0);
        }
      }

      // Track with monitor
      _monitor?.trackComponentRenderTime(component, renderTime);

      // Log significant render times
      if (kDebugMode && renderTime.inMilliseconds > 20) {
        AnxLog.info(
          '📊 Component: $component rendered in ${renderTime.inMilliseconds}ms',
        );
      }
    }
  }

  /// Track NavigationRail specific metrics
  void trackNavigationRailMetrics({
    required int menuItemCount,
    required bool hasSearchBar,
    required bool isExpanded,
    required int activeAnimations,
  }) {
    if (_monitor == null) return;

    // Calculate NavigationRail complexity
    int complexity = menuItemCount * 2; // Base complexity
    if (hasSearchBar) complexity += 5;
    if (isExpanded) complexity += 3;
    complexity += activeAnimations * 2;

    _monitor!.trackLayoutComplexity(
      visibleComponents: menuItemCount + (hasSearchBar ? 1 : 0),
      simultaneousAnimations: activeAnimations,
    );

    if (kDebugMode) {
      AnxLog.info('🧭 NavigationRail: $menuItemCount items, '
          'searchBar: $hasSearchBar, expanded: $isExpanded, '
          'animations: $activeAnimations, complexity: $complexity');
    }
  }

  /// Start NavigationRail performance profiling
  void startNavigationRailProfiling(String railId) {
    final stopwatch = Stopwatch()..start();
    _componentStopwatches['navigationRail_$railId'] = stopwatch;

    if (kDebugMode) {
      AnxLog.info('🧭 NavigationRail profiling started: $railId');
    }
  }

  /// End NavigationRail performance profiling and record metrics
  void endNavigationRailProfiling(
    String railId, {
    int? rebuildCount,
    bool? hadFrameDrops,
    Map<String, dynamic>? additionalMetrics,
  }) {
    final stopwatch = _componentStopwatches.remove('navigationRail_$railId');
    if (stopwatch == null) return;

    stopwatch.stop();
    final renderTime = stopwatch.elapsed;

    // Record render time history
    final historyKey = 'navigationRail_$railId';
    _renderTimeHistory.putIfAbsent(historyKey, () => <Duration>[]);
    _renderTimeHistory[historyKey]!.add(renderTime);

    // Keep only recent history (last 50 measurements)
    if (_renderTimeHistory[historyKey]!.length > 50) {
      _renderTimeHistory[historyKey]!.removeAt(0);
    }

    // Track component render time in monitor
    _monitor?.trackComponentRenderTime('NavigationRail_$railId', renderTime);

    // Log performance metrics
    if (kDebugMode) {
      final avgRenderTime = _calculateAverageRenderTime(historyKey);
      AnxLog.info('🧭 NavigationRail profiling completed: $railId\n'
          '   Render time: ${renderTime.inMicroseconds / 1000}ms\n'
          '   Average: ${avgRenderTime.inMicroseconds / 1000}ms\n'
          '   Rebuilds: ${rebuildCount ?? 'unknown'}\n'
          '   Frame drops: ${hadFrameDrops ?? false}\n'
          '   Additional: ${additionalMetrics ?? {}}');
    }

    // Check for performance issues
    _analyzeNavigationRailPerformance(
      railId,
      renderTime,
      rebuildCount,
      hadFrameDrops,
    );
  }

  /// Analyze NavigationRail performance and provide optimization hints
  void _analyzeNavigationRailPerformance(
    String railId,
    Duration renderTime,
    int? rebuildCount,
    bool? hadFrameDrops,
  ) {
    final issues = <String>[];
    final hints = <String>[];

    // Check render time threshold (16ms for 60fps)
    if (renderTime.inMilliseconds > 16) {
      issues.add('Slow render time: ${renderTime.inMicroseconds / 1000}ms');
      hints.add('Consider using RepaintBoundary around NavigationRail');
    }

    // Check rebuild frequency
    if (rebuildCount != null && rebuildCount > 3) {
      issues.add('Excessive rebuilds: $rebuildCount');
      hints.add('Optimize state management to reduce unnecessary rebuilds');
    }

    // Check frame drops
    if (hadFrameDrops == true) {
      issues.add('Frame drops detected during navigation');
      hints.add('Consider lazy loading or deferring heavy operations');
    }

    // Log issues and hints
    if (issues.isNotEmpty && kDebugMode) {
      AnxLog.warning('🧭 NavigationRail performance issues ($railId):');
      for (final issue in issues) {
        AnxLog.warning('   ⚠️ $issue');
      }
      AnxLog.info('💡 NavigationRail optimization hints:');
      for (final hint in hints) {
        AnxLog.info('   💡 $hint');
      }
    }
  }

  /// Calculate average render time for a component
  Duration _calculateAverageRenderTime(String historyKey) {
    final history = _renderTimeHistory[historyKey];
    if (history == null || history.isEmpty) return Duration.zero;

    final totalMicroseconds =
        history.map((d) => d.inMicroseconds).reduce((a, b) => a + b);

    return Duration(microseconds: totalMicroseconds ~/ history.length);
  }

  /// Analyze side menu widget tree complexity
  Map<String, dynamic> analyzeSideMenuComplexity(BuildContext context) {
    final analysis = <String, dynamic>{};

    try {
      // Analyze widget tree depth
      final treeDepth = _calculateSimpleTreeDepth(context);
      analysis['widgetTreeDepth'] = treeDepth;

      // Analyze NavigationRail specific complexity
      final railComplexity = _analyzeNavigationRailComplexity(context);
      analysis['navigationRailComplexity'] = railComplexity;

      // Analyze search bar complexity (if present)
      final searchBarComplexity = _analyzeSearchBarComplexity(context);
      analysis['searchBarComplexity'] = searchBarComplexity;

      // Calculate overall complexity score
      final complexityScore = _calculateComplexityScore(analysis);
      analysis['overallComplexityScore'] = complexityScore;

      // Provide optimization recommendations
      final recommendations = _generateOptimizationRecommendations(analysis);
      analysis['optimizationRecommendations'] = recommendations;

      if (kDebugMode) {
        AnxLog.info('🔍 Side Menu Complexity Analysis:');
        AnxLog.info('   Widget Tree Depth: $treeDepth');
        AnxLog.info('   NavigationRail Complexity: $railComplexity');
        AnxLog.info('   SearchBar Complexity: $searchBarComplexity');
        AnxLog.info('   Overall Score: $complexityScore');
        AnxLog.info('   Recommendations: ${recommendations.length}');
      }
    } catch (e) {
      AnxLog.warning('🔍 Side Menu complexity analysis failed: $e');
      analysis['error'] = e.toString();
    }

    return analysis;
  }

  /// Analyze NavigationRail specific complexity
  Map<String, dynamic> _analyzeNavigationRailComplexity(BuildContext context) {
    final complexity = <String, dynamic>{};

    try {
      // Count NavigationRail destinations
      int destinationCount = 0;
      bool hasCustomIcons = false;
      bool hasCustomLabels = false;

      // Try to find NavigationRail widget in the tree
      context.visitChildElements((element) {
        if (element.widget is NavigationRail) {
          final rail = element.widget as NavigationRail;
          destinationCount = rail.destinations.length;

          // Check for custom icons and labels
          for (final destination in rail.destinations) {
            if (destination.icon is! Icon) hasCustomIcons = true;
            if (destination.label is! Text) hasCustomLabels = true;
          }
        }
      });

      complexity['destinationCount'] = destinationCount;
      complexity['hasCustomIcons'] = hasCustomIcons;
      complexity['hasCustomLabels'] = hasCustomLabels;
      complexity['estimatedRenderCost'] = destinationCount * 2 +
          (hasCustomIcons ? 5 : 0) +
          (hasCustomLabels ? 3 : 0);
    } catch (e) {
      complexity['error'] = e.toString();
    }

    return complexity;
  }

  /// Analyze search bar complexity
  Map<String, dynamic> _analyzeSearchBarComplexity(BuildContext context) {
    final complexity = <String, dynamic>{};

    try {
      bool hasSearchBar = false;
      bool isAnimated = false;
      bool hasCustomStyling = false;

      // Try to find SearchBar widget in the tree
      context.visitChildElements((element) {
        if (element.widget is SearchBar) {
          hasSearchBar = true;
          final searchBar = element.widget as SearchBar;

          // Check for animations and custom styling
          if (searchBar.backgroundColor != null) hasCustomStyling = true;
          if (searchBar.elevation != null) hasCustomStyling = true;
          // Animation detection would require more complex analysis
        }
      });

      complexity['hasSearchBar'] = hasSearchBar;
      complexity['isAnimated'] = isAnimated;
      complexity['hasCustomStyling'] = hasCustomStyling;
      complexity['estimatedRenderCost'] = hasSearchBar
          ? (3 + (isAnimated ? 2 : 0) + (hasCustomStyling ? 1 : 0))
          : 0;
    } catch (e) {
      complexity['error'] = e.toString();
    }

    return complexity;
  }

  /// Calculate overall complexity score (0-100)
  int _calculateComplexityScore(Map<String, dynamic> analysis) {
    int score = 0;

    try {
      // Widget tree depth contribution (0-30 points)
      final treeDepth = analysis['widgetTreeDepth'] as int? ?? 0;
      score += (treeDepth * 1.5).clamp(0, 30).toInt();

      // NavigationRail complexity contribution (0-40 points)
      final railComplexity =
          analysis['navigationRailComplexity'] as Map<String, dynamic>? ?? {};
      final railCost = railComplexity['estimatedRenderCost'] as int? ?? 0;
      score += (railCost * 2).clamp(0, 40).toInt();

      // SearchBar complexity contribution (0-30 points)
      final searchComplexity =
          analysis['searchBarComplexity'] as Map<String, dynamic>? ?? {};
      final searchCost = searchComplexity['estimatedRenderCost'] as int? ?? 0;
      score += (searchCost * 5).clamp(0, 30).toInt();
    } catch (e) {
      AnxLog.warning('🔍 Complexity score calculation failed: $e');
    }

    return score.clamp(0, 100);
  }

  /// Generate optimization recommendations based on analysis
  List<String> _generateOptimizationRecommendations(
    Map<String, dynamic> analysis,
  ) {
    final recommendations = <String>[];

    try {
      final complexityScore = analysis['overallComplexityScore'] as int? ?? 0;
      final treeDepth = analysis['widgetTreeDepth'] as int? ?? 0;
      final railComplexity =
          analysis['navigationRailComplexity'] as Map<String, dynamic>? ?? {};
      final searchComplexity =
          analysis['searchBarComplexity'] as Map<String, dynamic>? ?? {};

      // High complexity recommendations
      if (complexityScore > 70) {
        recommendations
            .add('Consider using RepaintBoundary to isolate expensive widgets');
        recommendations
            .add('Implement lazy loading for non-visible menu items');
      }

      // Widget tree depth recommendations
      if (treeDepth > 25) {
        recommendations.add(
          'Widget tree is deep ($treeDepth levels) - consider flattening',
        );
        recommendations
            .add('Use const constructors where possible to reduce rebuilds');
      }

      // NavigationRail specific recommendations
      final destinationCount = railComplexity['destinationCount'] as int? ?? 0;
      if (destinationCount > 8) {
        recommendations
            .add('Consider grouping navigation items or using overflow menu');
      }

      if (railComplexity['hasCustomIcons'] == true) {
        recommendations.add(
          'Custom icons detected - ensure they are optimized for performance',
        );
      }

      // SearchBar specific recommendations
      if (searchComplexity['hasSearchBar'] == true) {
        recommendations
            .add('Consider debouncing search input to reduce rebuilds');
        if (searchComplexity['isAnimated'] == true) {
          recommendations
              .add('Optimize search bar animations for smooth performance');
        }
      }

      // General recommendations
      if (recommendations.isEmpty && complexityScore > 40) {
        recommendations
            .add('Monitor performance during navigation interactions');
        recommendations
            .add('Consider implementing widget caching for static elements');
      }
    } catch (e) {
      recommendations
          .add('Analysis incomplete - manual performance review recommended');
    }

    return recommendations;
  }

  /// Calculate simple widget tree depth for complexity analysis
  int _calculateSimpleTreeDepth(BuildContext context) {
    int depth = 0;
    BuildContext? currentContext = context;

    // Traverse up the widget tree to estimate depth
    while (currentContext != null && depth < 50) {
      // Safety limit
      depth++;
      try {
        // Get the parent context (simplified approach)
        currentContext =
            currentContext.findAncestorStateOfType<State>()?.context;
        if (currentContext == null) break;
      } catch (e) {
        break; // Stop if we can't traverse further
      }
    }

    return depth;
  }

  /// Track NavigationRail rebuild triggers
  final Map<String, List<String>> _rebuildTriggers = {};
  final Map<String, int> _rebuildCounts = {};
  final Map<String, DateTime> _lastRebuildTimes = {};

  /// Record a NavigationRail rebuild trigger
  void recordNavigationRailRebuild(
    String railId,
    String trigger, {
    Map<String, dynamic>? context,
  }) {
    final now = DateTime.now();

    // Initialize tracking for this rail if needed
    _rebuildTriggers.putIfAbsent(railId, () => <String>[]);
    _rebuildCounts.putIfAbsent(railId, () => 0);

    // Record the trigger
    _rebuildTriggers[railId]!.add(trigger);
    _rebuildCounts[railId] = (_rebuildCounts[railId] ?? 0) + 1;

    // Check for rapid rebuilds (potential performance issue)
    final lastRebuild = _lastRebuildTimes[railId];
    if (lastRebuild != null) {
      final timeSinceLastRebuild = now.difference(lastRebuild);
      if (timeSinceLastRebuild.inMilliseconds < 100) {
        // Rapid rebuild detected
        if (kDebugMode) {
          AnxLog.warning(
            '⚡ NavigationRail rapid rebuild detected: $railId\n'
            '   Trigger: $trigger\n'
            '   Time since last: ${timeSinceLastRebuild.inMilliseconds}ms\n'
            '   Total rebuilds: ${_rebuildCounts[railId]}\n'
            '   Context: ${context ?? 'none'}',
          );
        }
      }
    }

    _lastRebuildTimes[railId] = now;

    // Keep only recent triggers (last 20)
    if (_rebuildTriggers[railId]!.length > 20) {
      _rebuildTriggers[railId]!.removeAt(0);
    }

    // Log rebuild patterns for analysis
    if (kDebugMode && _rebuildCounts[railId]! % 5 == 0) {
      _analyzeRebuildPatterns(railId);
    }
  }

  /// Analyze rebuild patterns and provide optimization hints
  void _analyzeRebuildPatterns(String railId) {
    final triggers = _rebuildTriggers[railId] ?? [];
    final totalRebuilds = _rebuildCounts[railId] ?? 0;

    if (triggers.isEmpty) return;

    // Count trigger frequencies
    final triggerCounts = <String, int>{};
    for (final trigger in triggers) {
      triggerCounts[trigger] = (triggerCounts[trigger] ?? 0) + 1;
    }

    // Find most common triggers
    final sortedTriggers = triggerCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    if (kDebugMode) {
      AnxLog.info('🔍 NavigationRail rebuild analysis ($railId):');
      AnxLog.info('   Total rebuilds: $totalRebuilds');
      AnxLog.info('   Recent triggers (${triggers.length}):');

      for (final entry in sortedTriggers.take(5)) {
        final percentage =
            (entry.value / triggers.length * 100).toStringAsFixed(1);
        AnxLog.info('     ${entry.key}: ${entry.value} times ($percentage%)');
      }

      // Provide optimization hints
      final hints =
          _generateRebuildOptimizationHints(triggerCounts, totalRebuilds);
      if (hints.isNotEmpty) {
        AnxLog.info('💡 Rebuild optimization hints:');
        for (final hint in hints) {
          AnxLog.info('     • $hint');
        }
      }
    }
  }

  /// Generate optimization hints based on rebuild patterns
  List<String> _generateRebuildOptimizationHints(
    Map<String, int> triggerCounts,
    int totalRebuilds,
  ) {
    final hints = <String>[];

    // Check for excessive state changes
    final stateChanges = triggerCounts['state_change'] ?? 0;
    if (stateChanges > totalRebuilds * 0.3) {
      hints.add('High state change frequency - consider state optimization');
    }

    // Check for navigation-related rebuilds
    final navigationChanges = triggerCounts['navigation_change'] ?? 0;
    if (navigationChanges > totalRebuilds * 0.4) {
      hints.add(
        'Frequent navigation rebuilds - consider using keys or memoization',
      );
    }

    // Check for search-related rebuilds
    final searchChanges = triggerCounts['search_change'] ?? 0;
    if (searchChanges > totalRebuilds * 0.2) {
      hints.add('Search input causing rebuilds - implement debouncing');
    }

    // Check for theme/style changes
    final themeChanges = triggerCounts['theme_change'] ?? 0;
    if (themeChanges > 0) {
      hints.add('Theme changes detected - ensure proper theme listening');
    }

    // Check for parent widget rebuilds
    final parentRebuilds = triggerCounts['parent_rebuild'] ?? 0;
    if (parentRebuilds > totalRebuilds * 0.3) {
      hints.add(
        'Frequent parent rebuilds - consider RepaintBoundary or const widgets',
      );
    }

    // Check for animation-related rebuilds
    final animationRebuilds = triggerCounts['animation_tick'] ?? 0;
    if (animationRebuilds > totalRebuilds * 0.2) {
      hints.add(
        'Animation causing rebuilds - optimize animation implementation',
      );
    }

    // General optimization hints
    if (totalRebuilds > 50) {
      hints.add(
        'High rebuild count - review widget structure and state management',
      );
    }

    return hints;
  }

  /// Get rebuild statistics for a NavigationRail
  Map<String, dynamic> getNavigationRailRebuildStats(String railId) {
    return {
      'totalRebuilds': _rebuildCounts[railId] ?? 0,
      'recentTriggers': List<String>.from(_rebuildTriggers[railId] ?? []),
      'lastRebuildTime': _lastRebuildTimes[railId]?.toIso8601String(),
      'triggerFrequency': _calculateTriggerFrequency(railId),
    };
  }

  /// Calculate trigger frequency analysis
  Map<String, dynamic> _calculateTriggerFrequency(String railId) {
    final triggers = _rebuildTriggers[railId] ?? [];
    if (triggers.isEmpty) return {};

    final triggerCounts = <String, int>{};
    for (final trigger in triggers) {
      triggerCounts[trigger] = (triggerCounts[trigger] ?? 0) + 1;
    }

    final total = triggers.length;
    final frequency = <String, double>{};

    for (final entry in triggerCounts.entries) {
      frequency[entry.key] = entry.value / total;
    }

    return {
      'counts': triggerCounts,
      'frequencies': frequency,
      'totalSamples': total,
    };
  }

  /// Search bar animation performance tracking
  final Map<String, Stopwatch> _searchBarAnimationStopwatches = {};
  final Map<String, List<Duration>> _searchBarAnimationTimes = {};
  final Map<String, int> _searchBarFrameDrops = {};

  /// Start tracking search bar animation performance
  void startSearchBarAnimationProfiling(
    String animationId,
    String animationType,
  ) {
    final stopwatch = Stopwatch()..start();
    _searchBarAnimationStopwatches['${animationId}_$animationType'] = stopwatch;

    if (kDebugMode) {
      AnxLog.info(
        '🔍 Search bar animation started: $animationId ($animationType)',
      );
    }
  }

  /// End search bar animation profiling and record metrics
  void endSearchBarAnimationProfiling(
    String animationId,
    String animationType, {
    int? frameDrops,
    bool? wasSmooth,
    Map<String, dynamic>? additionalMetrics,
  }) {
    final key = '${animationId}_$animationType';
    final stopwatch = _searchBarAnimationStopwatches.remove(key);
    if (stopwatch == null) return;

    stopwatch.stop();
    final animationDuration = stopwatch.elapsed;

    // Record animation time history
    _searchBarAnimationTimes.putIfAbsent(key, () => <Duration>[]);
    _searchBarAnimationTimes[key]!.add(animationDuration);

    // Keep only recent history (last 30 animations)
    if (_searchBarAnimationTimes[key]!.length > 30) {
      _searchBarAnimationTimes[key]!.removeAt(0);
    }

    // Record frame drops
    if (frameDrops != null) {
      _searchBarFrameDrops[key] = (_searchBarFrameDrops[key] ?? 0) + frameDrops;
    }

    // Analyze animation performance
    _analyzeSearchBarAnimationPerformance(
      animationId,
      animationType,
      animationDuration,
      frameDrops,
      wasSmooth,
    );

    if (kDebugMode) {
      final avgDuration = _calculateAverageAnimationDuration(key);
      AnxLog.info(
          '🔍 Search bar animation completed: $animationId ($animationType)\n'
          '   Duration: ${animationDuration.inMilliseconds}ms\n'
          '   Average: ${avgDuration.inMilliseconds}ms\n'
          '   Frame drops: ${frameDrops ?? 0}\n'
          '   Smooth: ${wasSmooth ?? 'unknown'}\n'
          '   Additional: ${additionalMetrics ?? {}}');
    }
  }

  /// Analyze search bar animation performance
  void _analyzeSearchBarAnimationPerformance(
    String animationId,
    String animationType,
    Duration animationDuration,
    int? frameDrops,
    bool? wasSmooth,
  ) {
    final issues = <String>[];
    final hints = <String>[];

    // Check animation duration (should be under 300ms for good UX)
    if (animationDuration.inMilliseconds > 300) {
      issues.add('Slow animation: ${animationDuration.inMilliseconds}ms');
      hints.add('Consider reducing animation duration or complexity');
    }

    // Check for frame drops
    if (frameDrops != null && frameDrops > 0) {
      issues.add('Frame drops detected: $frameDrops');
      hints.add('Optimize animation implementation to reduce frame drops');
    }

    // Check smoothness
    if (wasSmooth == false) {
      issues.add('Animation reported as not smooth');
      hints.add(
        'Consider using different animation curves or reducing complexity',
      );
    }

    // Log issues and hints
    if (issues.isNotEmpty && kDebugMode) {
      AnxLog.warning(
        '🔍 Search bar animation issues ($animationId $animationType):',
      );
      for (final issue in issues) {
        AnxLog.warning('   ⚠️ $issue');
      }
      AnxLog.info('💡 Search bar animation optimization hints:');
      for (final hint in hints) {
        AnxLog.info('   💡 $hint');
      }
    }
  }

  /// Calculate average animation duration
  Duration _calculateAverageAnimationDuration(String key) {
    final history = _searchBarAnimationTimes[key];
    if (history == null || history.isEmpty) return Duration.zero;

    final totalMicroseconds =
        history.map((d) => d.inMicroseconds).reduce((a, b) => a + b);

    return Duration(microseconds: totalMicroseconds ~/ history.length);
  }

  /// NavigationRail performance baseline tracking
  final Map<String, NavigationRailBaseline> _navigationRailBaselines = {};

  /// Create performance baseline for NavigationRail
  void createNavigationRailBaseline(
    String railId, {
    required int destinationCount,
    required bool hasSearchBar,
    required bool isExtended,
    required BuildContext context,
  }) {
    final baseline = NavigationRailBaseline(
      railId: railId,
      destinationCount: destinationCount,
      hasSearchBar: hasSearchBar,
      isExtended: isExtended,
      isTablet: DesignSystem.isTablet(context),
      isLandscape:
          ResponsiveSystem.getOrientation(context) == Orientation.landscape,
      createdAt: DateTime.now(),
    );

    _navigationRailBaselines[railId] = baseline;

    if (kDebugMode) {
      AnxLog.info(
        '📊 NavigationRail baseline created: $railId\n'
        '   Destinations: $destinationCount\n'
        '   Search bar: $hasSearchBar\n'
        '   Extended: $isExtended\n'
        '   Form factor: ${baseline.isTablet ? 'tablet' : 'mobile'}\n'
        '   Orientation: ${baseline.isLandscape ? 'landscape' : 'portrait'}',
      );
    }
  }

  /// Update NavigationRail baseline with performance metrics
  void updateNavigationRailBaseline(
    String railId, {
    Duration? renderTime,
    int? rebuildCount,
    int? frameDrops,
    double? animationSmoothness,
    Map<String, dynamic>? additionalMetrics,
  }) {
    final baseline = _navigationRailBaselines[railId];
    if (baseline == null) return;

    // Update metrics
    if (renderTime != null) {
      baseline.addRenderTime(renderTime);
    }

    if (rebuildCount != null) {
      baseline.totalRebuilds = rebuildCount;
    }

    if (frameDrops != null) {
      baseline.addFrameDrops(frameDrops);
    }

    if (animationSmoothness != null) {
      baseline.addAnimationSmoothness(animationSmoothness);
    }

    if (additionalMetrics != null) {
      baseline.additionalMetrics.addAll(additionalMetrics);
    }

    // Check if baseline is complete and analyze
    if (baseline.isComplete) {
      _analyzeNavigationRailBaseline(baseline);
    }
  }

  /// Analyze NavigationRail baseline performance
  void _analyzeNavigationRailBaseline(NavigationRailBaseline baseline) {
    final analysis = baseline.getPerformanceAnalysis();

    if (kDebugMode) {
      AnxLog.info('📊 NavigationRail baseline analysis (${baseline.railId}):');
      AnxLog.info('   Average render time: ${analysis['averageRenderTime']}ms');
      AnxLog.info('   Total rebuilds: ${analysis['totalRebuilds']}');
      AnxLog.info('   Frame drop rate: ${analysis['frameDropRate']}%');
      AnxLog.info(
        '   Animation smoothness: ${analysis['averageAnimationSmoothness']}%',
      );
      AnxLog.info('   Performance score: ${analysis['performanceScore']}/100');

      final recommendations = analysis['recommendations'] as List<String>;
      if (recommendations.isNotEmpty) {
        AnxLog.info('💡 Baseline recommendations:');
        for (final recommendation in recommendations) {
          AnxLog.info('   • $recommendation');
        }
      }
    }
  }

  /// Get NavigationRail baseline metrics
  NavigationRailBaseline? getNavigationRailBaseline(String railId) {
    return _navigationRailBaselines[railId];
  }

  /// Compare current performance against baseline
  Map<String, dynamic> compareAgainstBaseline(
    String railId, {
    required Duration currentRenderTime,
    required int currentRebuilds,
    required int currentFrameDrops,
  }) {
    final baseline = _navigationRailBaselines[railId];
    if (baseline == null) {
      return {'error': 'No baseline found for $railId'};
    }

    final analysis = baseline.getPerformanceAnalysis();
    final baselineRenderTime = analysis['averageRenderTime'] as double;
    final baselineRebuilds = analysis['totalRebuilds'] as int;
    final baselineFrameDrops = analysis['totalFrameDrops'] as int;

    final comparison = <String, dynamic>{
      'renderTimeComparison': {
        'current': currentRenderTime.inMicroseconds / 1000,
        'baseline': baselineRenderTime,
        'difference':
            (currentRenderTime.inMicroseconds / 1000) - baselineRenderTime,
        'percentageChange': baselineRenderTime > 0
            ? ((currentRenderTime.inMicroseconds / 1000) - baselineRenderTime) /
                baselineRenderTime *
                100
            : 0,
      },
      'rebuildComparison': {
        'current': currentRebuilds,
        'baseline': baselineRebuilds,
        'difference': currentRebuilds - baselineRebuilds,
        'percentageChange': baselineRebuilds > 0
            ? (currentRebuilds - baselineRebuilds) / baselineRebuilds * 100
            : 0,
      },
      'frameDropComparison': {
        'current': currentFrameDrops,
        'baseline': baselineFrameDrops,
        'difference': currentFrameDrops - baselineFrameDrops,
        'percentageChange': baselineFrameDrops > 0
            ? (currentFrameDrops - baselineFrameDrops) /
                baselineFrameDrops *
                100
            : 0,
      },
    };

    // Generate performance verdict
    final renderTimeDiff =
        comparison['renderTimeComparison']['percentageChange'] as double;
    final rebuildDiff =
        comparison['rebuildComparison']['percentageChange'] as double;
    final frameDropDiff =
        comparison['frameDropComparison']['percentageChange'] as double;

    String verdict = 'stable';
    if (renderTimeDiff > 20 || rebuildDiff > 30 || frameDropDiff > 50) {
      verdict = 'degraded';
    } else if (renderTimeDiff < -10 &&
        rebuildDiff < -10 &&
        frameDropDiff < -20) {
      verdict = 'improved';
    }

    comparison['verdict'] = verdict;
    comparison['baselineId'] = railId;
    comparison['comparisonTime'] = DateTime.now().toIso8601String();

    if (kDebugMode && verdict != 'stable') {
      AnxLog.info(
        '📊 NavigationRail performance comparison ($railId): $verdict',
      );
      if (verdict == 'degraded') {
        AnxLog.warning('   ⚠️ Performance has degraded from baseline');
      } else {
        AnxLog.info('   ✅ Performance has improved from baseline');
      }
    }

    return comparison;
  }

  /// Track search bar interaction impact on NavigationRail
  void trackSearchBarImpactOnNavigationRail(
    String railId, {
    required bool searchBarVisible,
    required bool isAnimating,
    required Duration lastAnimationDuration,
    int? navigationRailFrameDrops,
  }) {
    // Track impact metrics for analysis
    if (kDebugMode) {
      final impact = <String, dynamic>{
        'searchBarVisible': searchBarVisible,
        'isAnimating': isAnimating,
        'lastAnimationDuration': lastAnimationDuration.inMilliseconds,
        'navigationRailFrameDrops': navigationRailFrameDrops ?? 0,
      };

      // Log impact data for development insights
      AnxLog.info('🔍 Search bar impact on NavigationRail: $impact');
    }

    // Analyze impact on NavigationRail performance
    if (isAnimating &&
        navigationRailFrameDrops != null &&
        navigationRailFrameDrops > 0) {
      if (kDebugMode) {
        AnxLog.warning(
          '🔍 Search bar animation impacting NavigationRail performance:\n'
          '   Rail ID: $railId\n'
          '   Animation duration: ${lastAnimationDuration.inMilliseconds}ms\n'
          '   NavigationRail frame drops: $navigationRailFrameDrops\n'
          '   Recommendation: Consider isolating search bar with RepaintBoundary',
        );
      }
    }

    // Track with monitor if available
    _monitor?.trackComponentRenderTime(
      'SearchBarImpact_$railId',
      lastAnimationDuration,
    );
  }

  /// Track master-detail layout performance
  void trackMasterDetailMetrics({
    required bool isMasterVisible,
    required bool isDetailVisible,
    required int activeProviders,
    required bool isTransitioning,
  }) {
    if (_monitor == null) return;

    int visibleComponents = 0;
    if (isMasterVisible) visibleComponents++;
    if (isDetailVisible) visibleComponents++;

    _monitor!.trackLayoutComplexity(
      activeProviders: activeProviders,
      visibleComponents: visibleComponents,
      simultaneousAnimations: isTransitioning ? 1 : 0,
    );

    if (kDebugMode) {
      AnxLog.info(
          '📱 MasterDetail: master: $isMasterVisible, detail: $isDetailVisible, '
          'providers: $activeProviders, transitioning: $isTransitioning');
    }
  }

  /// Track WebView performance metrics
  void trackWebViewMetrics({
    required bool isLoading,
    required int frameDrops,
    required Duration lastRenderTime,
    required String contentType,
  }) {
    if (_monitor == null) return;

    // Track WebView render time
    _monitor!.trackComponentRenderTime('webView', lastRenderTime);

    // Log WebView performance issues
    if (frameDrops > 0 && kDebugMode) {
      AnxLog.warning(
        '📖 WebView: $frameDrops frame drops detected for $contentType content',
      );
    }

    if (lastRenderTime.inMilliseconds > 30 && kDebugMode) {
      AnxLog.warning(
          '📖 WebView: Slow render time ${lastRenderTime.inMilliseconds}ms '
          'for $contentType');
    }
  }

  /// Analyze widget tree complexity
  void analyzeWidgetTreeComplexity(BuildContext context) {
    if (_monitor == null) return;

    // Calculate widget tree depth (simplified estimation)
    int depth = 0;
    BuildContext? currentContext = context;

    // Traverse up the widget tree to estimate depth
    while (currentContext != null && depth < 50) {
      // Safety limit
      depth++;
      try {
        // Find parent context by traversing up the widget tree
        final parentWidget =
            currentContext.findAncestorWidgetOfExactType<Widget>();
        if (parentWidget == null) break;

        // Get the parent context (simplified approach)
        currentContext =
            currentContext.findAncestorStateOfType<State>()?.context;
        if (currentContext == null) break;
      } catch (e) {
        break; // Stop if we can't traverse further
      }
    }

    _monitor!.trackLayoutComplexity(widgetTreeDepth: depth);

    if (kDebugMode && depth > 20) {
      AnxLog.info(
        '🌳 Widget tree depth: $depth (consider optimization if >25)',
      );
    }
  }

  /// Get component performance summary
  Map<String, dynamic> getComponentPerformanceSummary() {
    final summary = <String, dynamic>{};

    _renderTimeHistory.forEach((component, history) {
      if (history.isNotEmpty) {
        final avgMs = history
                .map((d) => d.inMicroseconds / 1000)
                .reduce((a, b) => a + b) /
            history.length;

        final maxMs = history
            .map((d) => d.inMicroseconds / 1000)
            .reduce((a, b) => a > b ? a : b);

        summary[component] = {
          'averageMs': avgMs.toStringAsFixed(1),
          'maxMs': maxMs.toStringAsFixed(1),
          'sampleCount': history.length,
        };
      }
    });

    return summary;
  }

  /// Check if device is in tablet mode
  bool isTabletMode(BuildContext context) {
    return DesignSystem.isTablet(context);
  }

  /// Check if device is in landscape mode
  bool isLandscapeMode(BuildContext context) {
    return ResponsiveSystem.getOrientation(context) == Orientation.landscape;
  }

  /// Update form factor for monitoring
  void updateFormFactor(BuildContext context) {
    if (_monitor == null) return;

    final isTablet = isTabletMode(context);
    final isLandscape = isLandscapeMode(context);

    _monitor!.updateFormFactor(isTablet, isLandscape);
  }

  /// Dispose resources
  void dispose() {
    _componentStopwatches.clear();
    _renderTimeHistory.clear();
    _monitor = null;

    if (kDebugMode) {
      AnxLog.info('📊 TabletPerformanceUtils: Disposed');
    }
  }
}

/// Widget wrapper for automatic performance tracking
class PerformanceTrackingWidget extends StatefulWidget {
  const PerformanceTrackingWidget({
    super.key,
    required this.componentName,
    required this.child,
    this.trackComplexity = false,
  });

  final String componentName;
  final Widget child;
  final bool trackComplexity;

  @override
  State<PerformanceTrackingWidget> createState() =>
      _PerformanceTrackingWidgetState();
}

class _PerformanceTrackingWidgetState extends State<PerformanceTrackingWidget> {
  @override
  void initState() {
    super.initState();

    // Start tracking when widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      TabletPerformanceUtils.instance
          .startComponentTracking(widget.componentName);
    });
  }

  @override
  void didUpdateWidget(PerformanceTrackingWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Track rebuilds
    WidgetsBinding.instance.addPostFrameCallback((_) {
      TabletPerformanceUtils.instance
          .startComponentTracking(widget.componentName);
    });
  }

  @override
  Widget build(BuildContext context) {
    // Update form factor
    TabletPerformanceUtils.instance.updateFormFactor(context);

    // Analyze widget tree complexity if requested
    if (widget.trackComplexity) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        TabletPerformanceUtils.instance.analyzeWidgetTreeComplexity(context);
      });
    }

    return widget.child;
  }

  @override
  void dispose() {
    // Stop tracking when widget is disposed
    TabletPerformanceUtils.instance.stopComponentTracking(widget.componentName);
    super.dispose();
  }
}
