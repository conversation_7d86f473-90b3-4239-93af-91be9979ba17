import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/performance/frame_rate_monitor.dart';
import 'package:dasso_reader/utils/performance/tablet_performance_utils.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/responsive_system.dart';

/// Tablet performance validation utility
///
/// This utility provides methods to validate the tablet performance monitoring system
/// and ensure it works correctly on both Android and iOS tablets without breaking
/// mobile performance monitoring.
class TabletPerformanceValidator {
  static TabletPerformanceValidator? _instance;
  static TabletPerformanceValidator get instance => 
      _instance ??= TabletPerformanceValidator._();
  
  TabletPerformanceValidator._();
  
  bool _isValidating = false;
  final List<String> _validationResults = [];
  
  /// Run comprehensive validation of tablet performance monitoring
  Future<Map<String, dynamic>> validateTabletPerformanceMonitoring(
    TabletPerformanceMonitor monitor,
    BuildContext context,
  ) async {
    if (_isValidating) {
      return {'status': 'error', 'message': 'Validation already in progress'};
    }
    
    _isValidating = true;
    _validationResults.clear();
    
    try {
      AnxLog.info('🧪 TabletPerformanceValidator: Starting validation');
      
      // Detect form factor
      final isTablet = DesignSystem.isTablet(context);
      final isLandscape = 
          ResponsiveSystem.getOrientation(context) == Orientation.landscape;
      
      _logValidationStep('Form factor detection', 
          'Tablet: $isTablet, Landscape: $isLandscape');
      
      // Update form factor in monitor
      monitor.updateFormFactor(isTablet, isLandscape);
      _logValidationStep('Form factor update', 'Successfully updated monitor');
      
      // Validate component tracking
      await _validateComponentTracking(monitor);
      
      // Validate layout complexity tracking
      _validateLayoutComplexityTracking(monitor);
      
      // Validate responsive layout tracking
      await _validateResponsiveLayoutTracking(monitor);
      
      // Validate metrics collection
      final metrics = _validateMetricsCollection(monitor);
      
      // Validate zero impact on mobile monitoring
      await _validateMobileCompatibility(monitor);
      
      _isValidating = false;
      
      return {
        'status': 'success',
        'results': _validationResults,
        'metrics': metrics,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      _logValidationStep('Error', 'Validation failed: $e');
      _isValidating = false;
      
      return {
        'status': 'error',
        'message': 'Validation failed: $e',
        'results': _validationResults,
      };
    }
  }
  
  /// Validate component tracking
  Future<void> _validateComponentTracking(TabletPerformanceMonitor monitor) async {
    _logValidationStep('Component tracking', 'Testing component render time tracking');
    
    // Test component tracking with simulated render times
    final components = [
      'navigationRail',
      'masterDetailLayout',
      'sideMenu',
      'searchBar',
      'profilePane',
      'webView',
    ];
    
    for (final component in components) {
      // Simulate component render time
      final renderTime = Duration(milliseconds: (15 + components.indexOf(component) * 5));
      monitor.trackComponentRenderTime(component, renderTime);
      
      _logValidationStep('Component: $component', 
          'Tracked render time: ${renderTime.inMilliseconds}ms');
      
      // Small delay between components
      await Future.delayed(const Duration(milliseconds: 10));
    }
  }
  
  /// Validate layout complexity tracking
  void _validateLayoutComplexityTracking(TabletPerformanceMonitor monitor) {
    _logValidationStep('Layout complexity', 'Testing layout complexity tracking');
    
    // Test layout complexity tracking
    monitor.trackLayoutComplexity(
      widgetTreeDepth: 18,
      activeProviders: 12,
      simultaneousAnimations: 2,
      visibleComponents: 8,
    );
    
    _logValidationStep('Layout complexity', 'Successfully tracked complexity metrics');
  }
  
  /// Validate responsive layout tracking
  Future<void> _validateResponsiveLayoutTracking(TabletPerformanceMonitor monitor) async {
    _logValidationStep('Responsive layout', 'Testing responsive layout tracking');
    
    // Test responsive layout tracking
    final layoutTypes = ['masterDetail', 'sideMenu', 'tabletGrid'];
    
    for (final layoutType in layoutTypes) {
      // Simulate layout render time
      final renderTime = Duration(milliseconds: (20 + layoutTypes.indexOf(layoutType) * 8));
      
      // Simulate layout metrics
      final layoutMetrics = {
        'breakpointCount': 3 + layoutTypes.indexOf(layoutType),
        'adaptiveComponents': 5 + layoutTypes.indexOf(layoutType) * 2,
        'conditionalLayouts': 2 + layoutTypes.indexOf(layoutType),
      };
      
      // Track responsive layout performance
      monitor.trackResponsiveLayoutPerformance(
        layoutType: layoutType,
        renderTime: renderTime,
        layoutMetrics: layoutMetrics,
      );
      
      _logValidationStep('Layout: $layoutType', 
          'Tracked render time: ${renderTime.inMilliseconds}ms');
      
      // Small delay between layouts
      await Future.delayed(const Duration(milliseconds: 10));
    }
  }
  
  /// Validate metrics collection
  Map<String, dynamic> _validateMetricsCollection(TabletPerformanceMonitor monitor) {
    _logValidationStep('Metrics collection', 'Validating metrics collection');
    
    // Get tablet metrics
    final metrics = monitor.getTabletMetrics();
    
    // Validate metrics
    final summary = metrics.getPerformanceSummary();
    
    _logValidationStep('Metrics validation', 
        'Collected ${summary.length} metric categories');
    
    return summary;
  }
  
  /// Validate zero impact on mobile monitoring
  Future<void> _validateMobileCompatibility(TabletPerformanceMonitor monitor) async {
    _logValidationStep('Mobile compatibility', 'Validating zero impact on mobile monitoring');
    
    // Simulate mobile mode
    monitor.updateFormFactor(false, false);
    
    // Try tracking component in mobile mode (should be ignored)
    monitor.trackComponentRenderTime('testComponent', const Duration(milliseconds: 50));
    
    // Get metrics and verify no tablet-specific data is collected in mobile mode
    final metrics = monitor.getTabletMetrics();
    final isMobileMode = !metrics.isTabletMode;
    
    _logValidationStep('Mobile compatibility', 
        'Mobile mode detection: $isMobileMode (expected: true)');
    
    // Reset to previous mode
    monitor.updateFormFactor(true, true);
    
    await Future.delayed(const Duration(milliseconds: 10));
  }
  
  /// Log validation step
  void _logValidationStep(String step, String result) {
    final logEntry = '[$step] $result';
    _validationResults.add(logEntry);
    
    if (kDebugMode) {
      AnxLog.info('🧪 Validation: $logEntry');
    }
  }
  
  /// Get validation results
  List<String> getValidationResults() {
    return List.from(_validationResults);
  }
  
  /// Check if validation is in progress
  bool get isValidating => _isValidating;
}
