import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dasso_reader/providers/storage_info.dart';
import 'package:dasso_reader/providers/anx_webdav.dart';
import 'package:dasso_reader/providers/fonts.dart';
import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/utils/performance/tablet_performance_utils.dart';

/// Optimized state management for profile/settings components
/// 
/// This system provides selective rebuilds and performance optimizations
/// specifically for the master-detail layout in tablet landscape mode.
class ProfileStateOptimization {
  static final TabletPerformanceUtils _performanceUtils = TabletPerformanceUtils.instance;

  /// Optimized selector for WebDAV status that only rebuilds when status changes
  static Provider<bool> webdavStatusProvider = Provider<bool>((ref) {
    // Track provider performance
    _performanceUtils.startComponentTracking('webdavStatusProvider');
    
    final webdavState = ref.watch(anxWebdavProvider);
    final isEnabled = Prefs().webdavStatus;
    
    // Complete tracking
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _performanceUtils.stopComponentTracking('webdavStatusProvider');
    });
    
    return isEnabled;
  });

  /// Optimized selector for WebDAV sync status that only rebuilds during sync
  static Provider<bool> webdavSyncingProvider = Provider<bool>((ref) {
    _performanceUtils.startComponentTracking('webdavSyncingProvider');
    
    final webdavState = ref.watch(anxWebdavProvider);
    final isSyncing = webdavState.isSyncing;
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _performanceUtils.stopComponentTracking('webdavSyncingProvider');
    });
    
    return isSyncing;
  });

  /// Optimized selector for storage info summary (only size totals)
  static Provider<Map<String, int>> storageInfoSummaryProvider = Provider<Map<String, int>>((ref) {
    _performanceUtils.startComponentTracking('storageInfoSummaryProvider');
    
    final storageAsync = ref.watch(storageInfoProvider);
    
    final summary = storageAsync.when(
      data: (storage) => {
        'totalSize': storage.databaseSize + storage.booksSize + storage.fontSize + 
                    storage.cacheSize + storage.logSize + storage.coverSize,
        'booksSize': storage.booksSize,
        'cacheSize': storage.cacheSize,
      },
      loading: () => <String, int>{},
      error: (_, __) => <String, int>{},
    );
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _performanceUtils.stopComponentTracking('storageInfoSummaryProvider');
    });
    
    return summary;
  });

  /// Optimized selector for font download status
  static Provider<Map<String, bool>> fontDownloadStatusProvider = Provider<Map<String, bool>>((ref) {
    _performanceUtils.startComponentTracking('fontDownloadStatusProvider');
    
    final fontDownloads = ref.watch(fontDownloadsProvider);
    final downloadStatus = <String, bool>{};
    
    for (final entry in fontDownloads.entries) {
      downloadStatus[entry.key] = entry.value.isDownloading;
    }
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _performanceUtils.stopComponentTracking('fontDownloadStatusProvider');
    });
    
    return downloadStatus;
  });

  /// Optimized theme mode provider that only rebuilds when theme changes
  static Provider<String> themeStatusProvider = Provider<String>((ref) {
    _performanceUtils.startComponentTracking('themeStatusProvider');
    
    // Watch for theme changes through SharedPreferences listener
    final themeMode = Prefs().themeMode.name;
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _performanceUtils.stopComponentTracking('themeStatusProvider');
    });
    
    return themeMode;
  });
}

/// Memoized widget cache for expensive settings components
class SettingsWidgetCache {
  static final Map<String, Widget> _cache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);

  /// Get cached widget or create new one
  static Widget getCachedWidget(String key, Widget Function() builder) {
    final now = DateTime.now();
    final timestamp = _cacheTimestamps[key];
    
    // Check if cache is valid
    if (timestamp != null && 
        now.difference(timestamp) < _cacheExpiry && 
        _cache.containsKey(key)) {
      
      if (kDebugMode) {
        TabletPerformanceUtils.instance.startComponentTracking('widgetCache_hit_$key');
        WidgetsBinding.instance.addPostFrameCallback((_) {
          TabletPerformanceUtils.instance.stopComponentTracking('widgetCache_hit_$key');
        });
      }
      
      return _cache[key]!;
    }
    
    // Create new widget and cache it
    if (kDebugMode) {
      TabletPerformanceUtils.instance.startComponentTracking('widgetCache_miss_$key');
    }
    
    final widget = builder();
    _cache[key] = widget;
    _cacheTimestamps[key] = now;
    
    if (kDebugMode) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        TabletPerformanceUtils.instance.stopComponentTracking('widgetCache_miss_$key');
      });
    }
    
    return widget;
  }

  /// Clear expired cache entries
  static void clearExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) >= _cacheExpiry) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      _cache.remove(key);
      _cacheTimestamps.remove(key);
    }
    
    if (kDebugMode && expiredKeys.isNotEmpty) {
      print('SettingsWidgetCache: Cleared ${expiredKeys.length} expired entries');
    }
  }

  /// Clear all cache
  static void clearAll() {
    _cache.clear();
    _cacheTimestamps.clear();
    
    if (kDebugMode) {
      print('SettingsWidgetCache: Cleared all cache entries');
    }
  }
}

/// Performance-aware state management for profile detail pane
class ProfileDetailStateManager {
  static final TabletPerformanceUtils _performanceUtils = TabletPerformanceUtils.instance;
  
  /// Track state changes with performance monitoring
  static void trackStateChange(String operation, VoidCallback stateChange) {
    _performanceUtils.startComponentTracking('profileState_$operation');
    
    // Execute state change
    stateChange();
    
    // Track completion
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _performanceUtils.stopComponentTracking('profileState_$operation');
    });
  }

  /// Batch multiple state changes for better performance
  static void batchStateChanges(String operation, List<VoidCallback> stateChanges) {
    _performanceUtils.startComponentTracking('profileStateBatch_$operation');
    
    // Execute all state changes in a single frame
    for (final stateChange in stateChanges) {
      stateChange();
    }
    
    // Track completion
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _performanceUtils.stopComponentTracking('profileStateBatch_$operation');
    });
  }

  /// Debounced state updates to prevent rapid rebuilds
  static final Map<String, Timer?> _debounceTimers = {};
  
  static void debounceStateChange(
    String key, 
    VoidCallback stateChange, 
    {Duration delay = const Duration(milliseconds: 100)}
  ) {
    // Cancel existing timer
    _debounceTimers[key]?.cancel();
    
    // Start new timer
    _debounceTimers[key] = Timer(delay, () {
      trackStateChange('debounced_$key', stateChange);
      _debounceTimers.remove(key);
    });
  }
}

/// Import for Timer
import 'dart:async';
