import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/widgets/common/adaptive_navigation.dart';
import 'package:dasso_reader/page/iap_page.dart';

import 'package:dasso_reader/page/settings_page/more_settings_page.dart';
import 'package:dasso_reader/page/settings_page/statistics_section.dart';
import 'package:dasso_reader/service/iap_service.dart';
import 'package:dasso_reader/utils/env_var.dart';
import 'package:dasso_reader/widgets/settings/about.dart';
import 'package:dasso_reader/widgets/settings/theme_mode.dart';
import 'package:dasso_reader/widgets/settings/user_profile_section.dart';
import 'package:dasso_reader/widgets/settings/webdav_switch.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SettingsPage extends ConsumerStatefulWidget {
  const SettingsPage({
    super.key,
    this.onNavigateToStats,
    this.onNavigateToMoreSettings,
  });

  final void Function(Widget page, String title)? onNavigateToStats;
  final void Function(Widget page, String title)? onNavigateToMoreSettings;

  @override
  ConsumerState<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends ConsumerState<SettingsPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ListView(
        children: [
          DesignSystem.verticalSpaceS, // Small padding at the top

          // User profile section with RepaintBoundary for performance
          const RepaintBoundary(
            child: UserProfileSection(),
          ),

          const Divider(),

          // Theme mode section with RepaintBoundary
          const RepaintBoundary(
            child: Padding(
              padding: EdgeInsets.fromLTRB(
                DesignSystem.spaceL - DesignSystem.spaceXS, // 20.0
                DesignSystem.spaceS, // 8.0
                DesignSystem.spaceS + DesignSystem.spaceXS, // 10.0 (8 + 2)
                DesignSystem.spaceS, // 8.0
              ),
              child: ChangeThemeMode(),
            ),
          ),

          const Divider(),

          // WebDAV settings with RepaintBoundary
          const RepaintBoundary(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: DesignSystem.spaceS),
              child: WebDavSettings(),
            ),
          ),

          const Divider(),

          // Statistics section with RepaintBoundary
          RepaintBoundary(
            child: StatisticsSection(onNavigate: widget.onNavigateToStats),
          ),

          const Divider(),

          // More settings with RepaintBoundary
          RepaintBoundary(
            child: MoreSettings(onNavigate: widget.onNavigateToMoreSettings),
          ),
          if (EnvVar.isAppStore)
            ListTile(
              title: Text(L10n.of(context).iap_page_title),
              leading: Icon(AdaptiveIcons.star),
              subtitle: Text(IAPService().statusTitle(context)),
              onTap: () {
                AdaptiveNavigation.push(context, const IAPPage());
              },
            ),

          const About(),
        ],
      ),
    );
  }
}
