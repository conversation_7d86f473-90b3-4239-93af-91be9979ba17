import 'dart:io';

import 'package:dasso_reader/enums/sync_direction.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/page/dictionary_page.dart';
import 'package:dasso_reader/page/home_page/bookshelf_page.dart';
import 'package:dasso_reader/page/home_page/hsk_page.dart';
import 'package:dasso_reader/page/home_page/notes_page.dart';
import 'package:dasso_reader/page/home_page/settings_page.dart';
import 'package:dasso_reader/page/home_page/vocabulary_page.dart';
import 'package:dasso_reader/service/convert_to_epub/txt/convert_from_text.dart';
import 'package:dasso_reader/widgets/add_book_menu.dart';
import 'package:dasso_reader/widgets/paste_text_fullscreen.dart';

import 'package:dasso_reader/service/book.dart';
import 'package:dasso_reader/utils/check_update.dart';
import 'package:dasso_reader/utils/get_path/get_temp_dir.dart';
import 'package:dasso_reader/utils/load_default_font.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/performance/tablet_performance_utils.dart';
import 'package:dasso_reader/providers/anx_webdav.dart';
import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/platform_adaptations.dart';
import 'package:dasso_reader/config/responsive_system.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/utils/platform/cross_platform_validator.dart';
import 'package:dasso_reader/widgets/common/adaptive_navigation.dart';

import 'package:dasso_reader/utils/toast/common.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:receive_sharing_intent/receive_sharing_intent.dart';
import 'package:file_picker/file_picker.dart';
import 'package:dasso_reader/config/navigation_system.dart' as nav;
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:dasso_reader/utils/state_management/app_state_manager.dart';
import 'package:dasso_reader/widgets/navigation/responsive_tab.dart';

WebViewEnvironment? webViewEnvironment;

class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _HomePageState();
}

// Using DesignSystem constants for consistent UI

class _HomePageState extends ConsumerState<HomePage>
    with TickerProviderStateMixin {
  int _currentIndex = 0;
  bool? _expanded;
  // Keep TabController for TabBar, add PageController for smooth transitions
  late TabController _tabController;
  late PageController _pageController;

  // Add search controller and state
  final TextEditingController _searchController = TextEditingController();
  bool _showSearch = false;

  // First, add a GlobalKey for the BookshelfPage
  final GlobalKey<BookshelfPageState> _bookshelfKey =
      GlobalKey<BookshelfPageState>();

  // Track current destination count for TabController recreation
  int _currentDestinationCount = 0;

  // Profile detail pane state for tablet landscape mode
  bool _showProfileInDetailPane = false;
  Widget? _profileDetailContent;

  // Performance monitoring for master-detail layout
  TabletPerformanceUtils get _performanceUtils =>
      TabletPerformanceUtils.instance;

  @override
  void initState() {
    super.initState();
    initAnx();

    // Initialize controllers with proper destination tracking
    _initializeControllers();

    // Add preference listener for live updates
    Prefs().addListener(_onPreferencesChanged);

    // Restore navigation state after frame is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _restoreNavigationState();
    });
  }

  /// Initialize TabController and PageController with current destinations
  void _initializeControllers() {
    final destinations = nav.NavigationSystem.getVisibleDestinations();
    _currentDestinationCount = destinations.length;

    // Create new controllers
    _tabController = TabController(length: destinations.length, vsync: this);
    _pageController = PageController(initialPage: _currentIndex);

    // Sync TabController with current index
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {
          _currentIndex = _tabController.index;
          _pageController.animateToPage(
            _currentIndex,
            duration: DesignSystem.durationFast,
            curve: Curves.easeInOut,
          );
        });
      }
    });
  }

  /// Handle preference changes for live UI updates
  void _onPreferencesChanged() {
    final destinations = nav.NavigationSystem.getVisibleDestinations();

    // Check if destination count changed (Notes tab toggled)
    if (destinations.length != _currentDestinationCount) {
      if (mounted) {
        setState(() {
          // Ensure current index is valid for new destination count
          if (_currentIndex >= destinations.length) {
            _currentIndex = 0; // Reset to first tab if current is invalid
          }

          // Dispose old TabController before creating new one
          _tabController.dispose();

          // Recreate TabController with new destination count
          _currentDestinationCount = destinations.length;
          _tabController = TabController(
            length: destinations.length,
            vsync: this,
          );

          // Sync TabController with current index
          _tabController.addListener(() {
            if (_tabController.indexIsChanging) {
              setState(() {
                _currentIndex = _tabController.index;
                _pageController.animateToPage(
                  _currentIndex,
                  duration: DesignSystem.durationFast,
                  curve: Curves.easeInOut,
                );
              });
            }
          });

          // Update PageController to current index
          _pageController.jumpToPage(_currentIndex);
        });
      }
    }
  }

  @override
  void dispose() {
    // Save navigation state before disposing
    _saveNavigationState();

    // Remove preference listener
    Prefs().removeListener(_onPreferencesChanged);

    // Dispose controllers
    _tabController.dispose();
    _pageController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// Save current navigation state
  void _saveNavigationState() {
    final destinations = nav.NavigationSystem.getVisibleDestinations();
    final currentDestination = _currentIndex < destinations.length
        ? destinations[_currentIndex]
        : destinations.first;

    AppStateManager().saveNavigationState(
      currentIndex: _currentIndex,
      currentRoute: currentDestination.id,
      additionalData: {
        'showSearch': _showSearch,
        'searchQuery': _searchController.text,
        'destinationCount': destinations.length,
      },
    );
  }

  /// Restore navigation state
  void _restoreNavigationState() {
    final state = AppStateManager().restoreNavigationState();
    if (state != null) {
      final savedIndex = state['currentIndex'] as int? ?? 0;
      final savedShowSearch =
          state['additionalData']?['showSearch'] as bool? ?? false;
      final savedSearchQuery =
          state['additionalData']?['searchQuery'] as String? ?? '';

      // Get current destinations count
      final destinations = nav.NavigationSystem.getVisibleDestinations();

      // Ensure the saved index is valid for current destination count
      if (savedIndex >= 0 && savedIndex < destinations.length) {
        setState(() {
          _currentIndex = savedIndex;

          // Sync both controllers
          _tabController.index = savedIndex;
          _pageController.jumpToPage(savedIndex);

          // Restore search state only for bookshelf tab
          if (_currentIndex == 0 && savedShowSearch) {
            _showSearch = true;
            _searchController.text = savedSearchQuery;

            // Restore search results
            if (savedSearchQuery.isNotEmpty) {
              final bookshelfState = _getBookshelfState();
              if (bookshelfState != null) {
                bookshelfState.searchBooks(savedSearchQuery);
              }
            }
          }
        });
      }
    }
  }

  Future<void> initAnx() async {
    AnxToast.init(context);

    // Defer update check to prevent setState during build
    SchedulerBinding.instance.addPostFrameCallback((_) {
      checkUpdate(false);
    });

    if (Prefs().webdavStatus) {
      await AnxWebdav().init();
      await AnxWebdav().syncData(SyncDirection.both, ref);
    }
    loadDefaultFont();

    // Mobile-only app: WebView environment is only needed for Windows (not supported)
    // Android and iOS use native WebView implementations
    if (CrossPlatformValidator.isWebViewSupported()) {
      AnxLog.info(
        'WebView support validated for ${PlatformAdaptations.platformName}',
      );
    } else {
      AnxLog.warning('WebView not supported on current platform');
    }

    if (PlatformAdaptations.isAndroid || PlatformAdaptations.isIOS) {
      // receive sharing intent
      Future<void> handleShare(List<SharedMediaFile> value) async {
        List<File> files = [];
        for (var item in value) {
          final sourceFile = File(item.path);
          files.add(sourceFile);
        }
        await importBookList(files, context, ref);
        ReceiveSharingIntent.instance.reset();
      }

      ReceiveSharingIntent.instance.getMediaStream().listen(
        (value) {
          AnxLog.info(
            'share: Receive share intent: ${value.map((e) => e.toMap())}',
          );
          if (value.isNotEmpty) {
            handleShare(value);
          }
        },
        onError: (err) {
          AnxLog.severe('share: Receive share intent');
        },
      );

      ReceiveSharingIntent.instance.getInitialMedia().then(
        (value) {
          AnxLog.info(
            'share: Receive share intent: ${value.map((e) => e.toMap())}',
          );
          if (value.isNotEmpty) {
            handleShare(value);
          }
        },
        onError: (err) {
          AnxLog.severe('share: Receive share intent');
        },
      );
    }
  }

  // Method to show settings with responsive behavior
  void _showSettings() {
    // Start performance tracking for master-detail layout transition
    _performanceUtils.startComponentTracking('masterDetailTransition');

    // Check if we're in tablet landscape mode (where NavigationRail is shown)
    final isTablet = DesignSystem.isTablet(context);
    final isLandscape =
        ResponsiveSystem.getOrientation(context) == Orientation.landscape;
    final shouldUseRail = isTablet && isLandscape;

    AnxLog.info('Profile button clicked - shouldUseRail: $shouldUseRail');

    if (shouldUseRail) {
      // For tablet landscape: Show profile in right pane while keeping NavigationRail
      AnxLog.info('Showing profile in detail pane');

      // Track master-detail layout metrics
      _performanceUtils.trackMasterDetailMetrics(
        isMasterVisible: true, // NavigationRail stays visible
        isDetailVisible: true, // Profile pane becomes visible
        activeProviders: 3, // Estimate: settings, theme, user profile
        isTransitioning: true,
      );

      setState(() {
        _showProfileInDetailPane = true;
        _profileDetailContent = const SettingsPage();
      });

      // Complete performance tracking after state update
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _performanceUtils.stopComponentTracking('masterDetailTransition');
      });
    } else {
      // For mobile: Use traditional full-screen navigation
      AnxLog.info('Showing profile in full screen');
      _performanceUtils.stopComponentTracking('masterDetailTransition');

      AdaptiveNavigation.push(
        context,
        Scaffold(
          appBar: AppBar(title: const Text('Profile')),
          body: const SettingsPage(),
        ),
      );
    }
  }

  /// Build enhanced side menu header with improved layout for tablet landscape mode
  /// Horizontal layout: App title above, then avatar and search bar inline for space efficiency
  Widget _buildEnhancedSideMenuHeader(
    BuildContext context,
    ColorScheme colorScheme,
  ) {
    return Column(
      children: [
        // App title positioned at the top for better hierarchy
        if (_expanded!)
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: DesignSystem.spaceM,
              vertical: DesignSystem.spaceS,
            ),
            child: Text(
              'DassoShu',
              semanticsLabel: 'DassoShu Reader App',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: DesignSystem.getAdjustedFontWeight(
                      FontWeight.w500,
                      text: 'DassoShu',
                    ),
                    color: colorScheme.primary,
                    letterSpacing: 0.5,
                  ),
            ),
          ),

        // Horizontal row with avatar and search bar for space efficiency
        Container(
          padding: const EdgeInsets.all(DesignSystem.spaceM),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Avatar button with enhanced styling
              SemanticHelpers.button(
                context: context,
                label: 'Settings',
                hint: 'Open app settings and preferences',
                onTap: _showSettings,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(DesignSystem.radiusM),
                    color: colorScheme.primaryContainer.withValues(alpha: 0.1),
                  ),
                  child: IconButton.filledTonal(
                    tooltip: 'Settings',
                    onPressed: _showSettings,
                    icon: Icon(
                      AdaptiveIcons.profile,
                      size: DesignSystem.widgetIconSizeMedium,
                    ),
                    style: IconButton.styleFrom(
                      backgroundColor: colorScheme.primaryContainer,
                      foregroundColor: colorScheme.onPrimaryContainer,
                      padding: const EdgeInsets.all(DesignSystem.spaceM),
                    ),
                  ),
                ),
              ),

              // Horizontal spacing between avatar and search
              const SizedBox(width: DesignSystem.spaceM),

              // Search bar positioned inline with avatar for space efficiency
              if (_currentIndex == 0) // Only show for bookshelf tab
                Flexible(
                  fit: FlexFit.loose,
                  child: AnimatedSwitcher(
                    duration: DesignSystem.durationFast,
                    switchInCurve: Curves.easeInOut,
                    switchOutCurve: Curves.easeInOut,
                    child: _showSearch
                        ? _buildExpandedSearchBar(context, colorScheme)
                        : _buildSearchButton(context, colorScheme),
                  ),
                ),
            ],
          ),
        ),

        // Spacing for visual separation
        const SizedBox(height: DesignSystem.spaceL),
      ],
    );
  }

  /// Build expanded search bar with Material Design 3 styling
  /// Optimized for horizontal layout in tablet landscape mode
  Widget _buildExpandedSearchBar(
    BuildContext context,
    ColorScheme colorScheme,
  ) {
    return Container(
      key: const ValueKey('search-expanded'),
      // Set constraints for horizontal layout
      constraints: BoxConstraints(
        maxWidth: _expanded! ? 200 : 56,
        minWidth: 56,
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: DesignSystem.spaceS,
        vertical: DesignSystem.spaceXS,
      ),
      child: SearchBar(
        controller: _searchController,
        hintText: L10n.of(context).search_books_hint,
        hintStyle: WidgetStateProperty.all(
          Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
              ),
        ),
        textStyle: WidgetStateProperty.all(
          Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface,
              ),
        ),
        backgroundColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.focused)) {
            return colorScheme.surfaceContainerLow;
          }
          return colorScheme.surfaceContainerLowest;
        }),
        elevation: WidgetStateProperty.all(DesignSystem.elevationXS),
        padding: const WidgetStatePropertyAll<EdgeInsets>(
          EdgeInsets.symmetric(horizontal: DesignSystem.spaceS),
        ),
        constraints: const BoxConstraints(
          minHeight: DesignSystem.widgetMinTouchTarget,
          maxHeight: DesignSystem.widgetMinTouchTarget,
        ),
        leading: Icon(
          AdaptiveIcons.search,
          size: DesignSystem.widgetIconSizeSmall,
          color: colorScheme.onSurfaceVariant,
        ),
        trailing: [
          SemanticHelpers.button(
            context: context,
            label: 'Clear Search',
            hint: 'Clear the search field and hide search',
            onTap: _clearSearch,
            child: IconButton(
              icon: Icon(
                AdaptiveIcons.close,
                size: DesignSystem.widgetIconSizeSmall,
                color: colorScheme.onSurfaceVariant,
              ),
              constraints: const BoxConstraints(
                minWidth: DesignSystem.spaceXL,
                minHeight: DesignSystem.spaceXL,
              ),
              padding: EdgeInsets.zero,
              onPressed: _clearSearch,
            ),
          ),
        ],
        onChanged: (value) {
          final bookshelfState = _getBookshelfState();
          if (bookshelfState != null) {
            bookshelfState.searchBooks(value.isEmpty ? null : value);
          }
        },
      ),
    );
  }

  /// Build search button with enhanced styling
  /// Optimized for horizontal layout in tablet landscape mode
  Widget _buildSearchButton(BuildContext context, ColorScheme colorScheme) {
    return SemanticHelpers.button(
      context: context,
      label: 'Search Books',
      hint: 'Open search field to find books in your library',
      onTap: _showSearchBar,
      child: Container(
        key: const ValueKey('search-icon'),
        constraints: const BoxConstraints(
          minWidth: 56,
          minHeight: 56,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(DesignSystem.radiusM),
          color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        ),
        child: IconButton(
          icon: Icon(
            AdaptiveIcons.search,
            size: DesignSystem.widgetIconSizeMedium,
          ),
          tooltip: 'Search books',
          style: IconButton.styleFrom(
            backgroundColor: colorScheme.surfaceContainerHighest,
            foregroundColor: colorScheme.onSurface,
            padding: const EdgeInsets.all(DesignSystem.spaceM),
          ),
          onPressed: _showSearchBar,
        ),
      ),
    );
  }

  /// Show search bar with proper focus handling
  void _showSearchBar() {
    HapticFeedback.selectionClick();
    final focusNode = FocusNode();
    setState(() {
      _showSearch = true;
    });

    // Schedule focus for iOS compatibility
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        if (PlatformAdaptations.isIOS) {
          focusNode.requestFocus();
        } else {
          focusNode.requestFocus();
        }
      }
    });
  }

  /// Clear search with proper state management
  void _clearSearch() {
    HapticFeedback.selectionClick();
    setState(() {
      _showSearch = false;
      _searchController.clear();
      final bookshelfState = _getBookshelfState();
      if (bookshelfState != null) {
        bookshelfState.searchBooks(null);
      }
    });
  }

  /// Build the profile detail layout for tablet landscape mode
  /// This replaces the main content with the profile content while keeping NavigationRail
  Widget _buildProfileDetailLayout(List<Widget> pages) {
    return _ProfileDetailPane(
      onClose: () {
        // Start performance tracking for master-detail layout close
        _performanceUtils.startComponentTracking('masterDetailClose');

        // Track master-detail layout metrics for closing
        _performanceUtils.trackMasterDetailMetrics(
          isMasterVisible: true, // NavigationRail stays visible
          isDetailVisible: false, // Profile pane becomes hidden
          activeProviders: 1, // Reduced providers after closing
          isTransitioning: true,
        );

        setState(() {
          _showProfileInDetailPane = false;
          _profileDetailContent = null;
        });

        // Complete performance tracking after state update
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _performanceUtils.stopComponentTracking('masterDetailClose');
        });
      },
      initialContent: _profileDetailContent ?? const SettingsPage(),
    );
  }

  // We'll keep this method for future implementation
  // void _showStatistics() {
  //   // Implementation removed as it's not currently used
  // }

  // Add a direct method for importing books
  Future<void> _importBook() async {
    try {
      // Directly call the import book functionality
      final result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: true,
      );

      if (result == null || result.files.isEmpty) {
        return;
      }

      List<PlatformFile> files = result.files;
      AnxLog.info('importBook files: ${files.toString()}');
      List<File> fileList = [];

      // FilePicker on Windows will return files with original path,
      // but on Android it will return files with temporary path.
      // So we need to save the files to the temp directory.
      if (!PlatformAdaptations.isAndroid) {
        fileList = await Future.wait(
          files.map((file) async {
            Directory tempDir = await getAnxTempDir();
            File tempFile = File('${tempDir.path}/${file.name}');
            await File(file.path!).copy(tempFile.path);
            return tempFile;
          }).toList(),
        );
      } else {
        fileList = files.map((file) => File(file.path!)).toList();
      }

      // Add mounted check to avoid using BuildContext across async gaps
      if (mounted) {
        await importBookList(fileList, context, ref);
      }
    } catch (e) {
      AnxLog.severe('_importBook: Failed to import books: $e');
      if (mounted) {
        AnxToast.show('Failed to import books: ${e.toString()}');
      }
    }
  }

  // Method to paste text from clipboard
  Future<void> _pasteTextFromClipboard() async {
    await PasteTextFullScreen.show(
      context,
      // Disable auto-paste to start fresh each time
      autoPasteFromClipboard: false,
      onSave: (title, content) async {
        // Show loading indicator
        SmartDialog.showLoading<void>(msg: 'Converting text...');

        try {
          // Convert the text to an EPUB file
          final epubFile = await convertFromText(content, title);

          // Import the EPUB file
          if (mounted) {
            await importBook(epubFile, ref);
            AnxToast.show('Added "$title" to your bookshelf');
          }
        } catch (e) {
          AnxLog.severe('Error converting text to EPUB: $e');
          AnxToast.show('Failed to create book from text');
        } finally {
          SmartDialog.dismiss<void>();
        }
      },
    );
  }

  // Show options menu for adding books
  void _showAddOptions() {
    showDialog<void>(
      context: context,
      barrierDismissible:
          true, // Ensure the dialog can be dismissed by tapping outside
      builder: (context) => showAddBookMenu(
        context: context,
        onPasteText: _pasteTextFromClipboard,
        onImportFile: _importBook,
      ),
    );
  }

  /// Get manufacturer-aware tab spacing for consistent appearance across devices
  /// Fixed spacing approach for identical visual separation across all devices
  /// Accounts for Notes tab toggle to maintain consistent look and feel
  double _getManufacturerAwareTabSpacing(BuildContext context) {
    // Get current visible destinations to account for Notes tab toggle
    final destinations = nav.NavigationSystem.getVisibleDestinations();
    final tabCount = destinations.length;

    // Use fixed base spacing for consistent visual separation
    // Adjust slightly based on tab count to maintain visual balance
    double baseSpacing;
    if (tabCount == 4) {
      baseSpacing =
          14.0; // Slightly more spacing with 4 tabs for better balance
    } else if (tabCount == 5) {
      baseSpacing =
          11.0; // Slightly less spacing with 5 tabs to prevent crowding
    } else {
      baseSpacing = 12.0; // Default spacing for any other count
    }

    // Apply manufacturer-specific fine-tuning while maintaining consistent visual gaps
    final manufacturerMultiplier =
        DesignSystem.getTextWidthCompensationMultiplier();

    // Calculate final spacing
    final finalSpacing = baseSpacing * manufacturerMultiplier;

    // Debug logging in development mode
    if (kDebugMode) {
      final debugInfo = DesignSystem.getManufacturerDebugInfo();
      print('TabBar Spacing Debug:');
      print('  Device: ${debugInfo['manufacturer']} ${debugInfo['model']}');
      print(
        '  Tab Count: $tabCount (Notes: ${Prefs().bottomNavigatorShowNote})',
      );
      print('  Base Spacing: ${baseSpacing}px');
      print('  Text Width Compensation: ${manufacturerMultiplier}x');
      print('  Final Spacing: ${finalSpacing.toStringAsFixed(2)}px');
    }

    // Return fixed spacing with minimal manufacturer adjustment for pixel-perfect consistency
    return finalSpacing;
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    // Create the list of pages
    List<Widget> pages = [
      BookshelfPage(key: _bookshelfKey),
      const DictionaryPage(), // Add Dictionary page
      const VocabularyPage(), // Add Vocabulary page
      const HskPage(), // Add HSK page
      if (Prefs().bottomNavigatorShowNote) const NotesPage(),
    ];

    // Get navigation destinations using the new system
    final destinations = nav.NavigationSystem.getVisibleDestinations();

    // Create the tab items for backward compatibility
    List<Tab> tabItems = destinations
        .map(
          (destination) => Tab(
            text: destination.getLabel(context),
            icon: Icon(destination.icon),
          ),
        )
        .toList();

    // Use scaffold for responsive design with orientation awareness
    return LayoutBuilder(
      builder: (context, constraints) {
        final isLandscape = ResponsiveSystem.isLandscape(context);
        final isTablet = DesignSystem.isTablet(context);
        final orientation = ResponsiveSystem.getOrientation(context);
        final screenWidth = ResponsiveSystem.getScreenWidth(context);
        final screenHeight = ResponsiveSystem.getScreenHeight(context);

        // Debug logging for orientation detection
        debugPrint('=== ORIENTATION DEBUG ===');
        debugPrint('isTablet: $isTablet');
        debugPrint('isDesktop: ${DesignSystem.isDesktop(context)}');
        debugPrint('isMobile: ${DesignSystem.isMobile(context)}');
        debugPrint('isLandscape: $isLandscape');
        debugPrint('orientation: $orientation');
        debugPrint('screenWidth: $screenWidth');
        debugPrint('screenHeight: $screenHeight');
        debugPrint(
          'devicePixelRatio: ${MediaQuery.of(context).devicePixelRatio}',
        );
        debugPrint('constraints.maxWidth: ${constraints.maxWidth}');
        debugPrint('constraints.maxHeight: ${constraints.maxHeight}');
        debugPrint(
          'breakpointLargePhone: ${DesignSystem.breakpointLargePhone}',
        );
        debugPrint('breakpointDesktop: ${DesignSystem.breakpointDesktop}');

        // Show side menu (NavigationRail) only for tablets in landscape orientation
        // Mobile devices always use bottom navigation (portrait-only)
        // Desktop devices always use side menu (not supported in this app)
        final shouldUseRail = isTablet && isLandscape;

        debugPrint('shouldUseRail: $shouldUseRail');
        debugPrint('========================');

        // Set NavigationRail to always be expanded for optimal tablet landscape viewing
        // Remove hamburger menu functionality for cleaner, professional appearance
        _expanded ??= true;

        // For desktop/large tablets or landscape tablets
        if (shouldUseRail) {
          return Scaffold(
            floatingActionButton: _currentIndex == 0
                ? SemanticHelpers.button(
                    context: context,
                    label: 'Add Book',
                    hint:
                        'Add a new book to your library by importing files or pasting text',
                    onTap: _showAddOptions,
                    child: FloatingActionButton(
                      onPressed: _showAddOptions,
                      tooltip: 'Add Book',
                      child: Icon(AdaptiveIcons.add),
                    ),
                  )
                : null,
            body: Row(
              children: [
                NavigationRail(
                  leading: _buildEnhancedSideMenuHeader(context, colorScheme),
                  // Removed hamburger menu button for cleaner NavigationRail appearance
                  // NavigationRail now maintains fixed expanded size for optimal tablet viewing
                  trailing: null,
                  extended: _expanded!,
                  selectedIndex: _currentIndex,
                  onDestinationSelected: (index) {
                    nav.NavigationSystem.provideFeedback(
                      nav.NavigationFeedbackType.selection,
                    );
                    setState(() {
                      _currentIndex = index;

                      // Debug navigation state
                      debugPrint('=== NAVIGATION DEBUG ===');
                      debugPrint('NavigationRail selected index: $index');
                      debugPrint('_currentIndex: $_currentIndex');
                      debugPrint(
                        '_tabController.index: ${_tabController.index}',
                      );
                      debugPrint(
                        '_pageController.page: ${_pageController.hasClients ? _pageController.page : 'not built'}',
                      );
                      debugPrint(
                        '_showProfileInDetailPane: $_showProfileInDetailPane',
                      );
                      debugPrint('========================');

                      // Close profile detail pane when navigating to other pages
                      // This ensures NavigationRail buttons work correctly in tablet landscape mode
                      if (_showProfileInDetailPane) {
                        AnxLog.info(
                          'Closing profile detail pane to show page: $index',
                        );

                        // Start performance tracking for navigation transition
                        _performanceUtils
                            .startComponentTracking('navigationTransition');

                        // Track master-detail layout metrics for navigation
                        _performanceUtils.trackMasterDetailMetrics(
                          isMasterVisible: true, // NavigationRail stays visible
                          isDetailVisible: false, // Profile pane becomes hidden
                          activeProviders: 2, // Main page providers
                          isTransitioning: true,
                        );

                        _showProfileInDetailPane = false;
                        _profileDetailContent = null;

                        // Sync TabController to keep everything in sync
                        _tabController.index = index;

                        // Defer PageController animation until after the PageView is rebuilt
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          if (mounted && _pageController.hasClients) {
                            _pageController.animateToPage(
                              index,
                              duration: DesignSystem.durationFast,
                              curve: Curves.easeInOut,
                            );
                          }
                          // Complete performance tracking after animation
                          _performanceUtils
                              .stopComponentTracking('navigationTransition');
                        });
                      } else {
                        // Normal navigation when profile detail pane is not open
                        // Sync TabController to keep everything in sync
                        _tabController.index = index;

                        // Navigate to the selected page immediately
                        if (_pageController.hasClients) {
                          _pageController.animateToPage(
                            index,
                            duration: DesignSystem.durationFast,
                            curve: Curves.easeInOut,
                          );
                        }
                      }

                      // Hide search when switching away from bookshelf tab
                      if (index != 0 && _showSearch) {
                        _showSearch = false;
                        _searchController.clear();
                        final bookshelfState = _getBookshelfState();
                        if (bookshelfState != null) {
                          bookshelfState.searchBooks(null);
                        }
                      }
                    });
                  },
                  destinations: tabItems
                      .map(
                        (tab) => NavigationRailDestination(
                          icon: tab.icon!,
                          label: Text(tab.text!),
                        ),
                      )
                      .toList(),
                  labelType: _expanded!
                      ? NavigationRailLabelType.none
                      : NavigationRailLabelType.all,
                  backgroundColor: ElevationOverlay.applySurfaceTint(
                    colorScheme.surface,
                    colorScheme.primary,
                    1,
                  ),
                ),
                Expanded(
                  child: _showProfileInDetailPane
                      ? _buildProfileDetailLayout(pages)
                      : PageView(
                          controller: _pageController,
                          onPageChanged: (index) {
                            setState(() {
                              _currentIndex = index;
                              // Hide search when switching away from bookshelf tab
                              if (index != 0 && _showSearch) {
                                _showSearch = false;
                                _searchController.clear();
                                final bookshelfState = _getBookshelfState();
                                if (bookshelfState != null) {
                                  bookshelfState.searchBooks(null);
                                }
                              }
                            });
                          },
                          children: pages,
                        ),
                ),
              ],
            ),
          );
        }
        // For mobile/smaller tablets
        else {
          return Scaffold(
            body: NestedScrollView(
              headerSliverBuilder: (context, innerBoxIsScrolled) {
                return [
                  SliverAppBar(
                    pinned: true, // Keep this to ensure AppBar stays visible
                    floating: false,
                    snap: false,
                    forceElevated: innerBoxIsScrolled,
                    // Orientation-aware height properties
                    toolbarHeight:
                        ResponsiveSystem.getOrientationAwareAppBarHeight(
                      context,
                    ),
                    expandedHeight:
                        ResponsiveSystem.getOrientationAwareAppBarHeight(
                              context,
                            ) +
                            22.0,
                    collapsedHeight:
                        ResponsiveSystem.getOrientationAwareAppBarHeight(
                              context,
                            ) +
                            22.0,
                    // Enhanced elevation for better depth perception
                    scrolledUnderElevation: DesignSystem.elevationM,
                    shadowColor: colorScheme.shadow,
                    // Fix: Use theme AppBar colors to support templates
                    surfaceTintColor: Colors.transparent,
                    backgroundColor:
                        Theme.of(context).appBarTheme.backgroundColor ??
                            colorScheme.surface,
                    foregroundColor:
                        Theme.of(context).appBarTheme.foregroundColor,
                    iconTheme: Theme.of(context).appBarTheme.iconTheme,
                    // Ensure the title is fixed and non-collapsing
                    titleSpacing: DesignSystem.spaceM,
                    automaticallyImplyLeading: false,
                    // Move the title content to flexibleSpace for proper handling
                    flexibleSpace: FlexibleSpaceBar(
                      titlePadding: EdgeInsets.zero,
                      // Ensure content is visible in collapsed state
                      expandedTitleScale: 1.0, // No scaling on expand/collapse
                      title: SafeArea(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            // Main AppBar row with avatar, title and search - no extra top padding
                            Padding(
                              padding: const EdgeInsets.only(
                                left: DesignSystem.spaceM,
                              ),
                              child: Row(
                                children: [
                                  // Avatar on the left - Enlarged and styled
                                  Container(
                                    width: 48,
                                    height: 48,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Theme.of(context)
                                                  .appBarTheme
                                                  .backgroundColor !=
                                              null
                                          ? Theme.of(context)
                                                  .appBarTheme
                                                  .foregroundColor ?? // White background when template is applied
                                              colorScheme.primaryContainer
                                          : colorScheme.primaryContainer,
                                    ),
                                    child: Material(
                                      color: Colors.transparent,
                                      child: InkWell(
                                        onTap: _showSettings,
                                        customBorder: const CircleBorder(),
                                        child: Padding(
                                          padding: const EdgeInsets.all(
                                            DesignSystem.spaceS + 4,
                                          ),
                                          child: Icon(
                                            AdaptiveIcons.profile,
                                            size: DesignSystem
                                                .widgetIconSizeMedium,
                                            color: Theme.of(context)
                                                        .appBarTheme
                                                        .backgroundColor !=
                                                    null
                                                ? Theme.of(context)
                                                        .appBarTheme
                                                        .backgroundColor ?? // Red icon when template is applied
                                                    colorScheme
                                                        .onPrimaryContainer
                                                : colorScheme
                                                    .onPrimaryContainer,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  // This expanded will push the title to center
                                  Expanded(
                                    child: _showSearch && _currentIndex == 0
                                        ? Padding(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: DesignSystem.spaceM,
                                              vertical: DesignSystem.spaceXS,
                                            ),
                                            child: SearchBar(
                                              controller: _searchController,
                                              hintText: L10n.of(
                                                context,
                                              ).search_books_hint,
                                              hintStyle:
                                                  WidgetStateProperty.all(
                                                Theme.of(context)
                                                    .textTheme
                                                    .bodyMedium
                                                    ?.copyWith(
                                                      color: Theme.of(context)
                                                              .appBarTheme
                                                              .foregroundColor
                                                              ?.withValues(
                                                                alpha: 0.7,
                                                              ) ??
                                                          colorScheme
                                                              .onSurfaceVariant,
                                                    ),
                                              ),
                                              textStyle:
                                                  WidgetStateProperty.all(
                                                Theme.of(context)
                                                    .textTheme
                                                    .bodyMedium
                                                    ?.copyWith(
                                                      color: Theme.of(context)
                                                              .appBarTheme
                                                              .foregroundColor ??
                                                          colorScheme.onSurface,
                                                    ),
                                              ),
                                              padding:
                                                  const WidgetStatePropertyAll<
                                                      EdgeInsets>(
                                                EdgeInsets.symmetric(
                                                  horizontal:
                                                      DesignSystem.spaceM,
                                                ),
                                              ),
                                              elevation:
                                                  const WidgetStatePropertyAll<
                                                      double>(0),
                                              backgroundColor:
                                                  WidgetStateProperty
                                                      .resolveWith<Color>(
                                                          (states) {
                                                if (states.contains(
                                                  WidgetState.focused,
                                                )) {
                                                  return colorScheme
                                                      .surfaceContainerLow;
                                                }
                                                return Colors.transparent;
                                              }),
                                              leading: Icon(
                                                AdaptiveIcons.search,
                                                color: Theme.of(context)
                                                        .appBarTheme
                                                        .foregroundColor
                                                        ?.withValues(
                                                          alpha: 0.7,
                                                        ) ??
                                                    colorScheme
                                                        .onSurfaceVariant,
                                                size: DesignSystem
                                                        .widgetIconSizeSmall +
                                                    4,
                                              ),
                                              trailing: [
                                                if (_searchController
                                                    .text.isNotEmpty)
                                                  IconButton(
                                                    icon: Icon(
                                                      AdaptiveIcons.close,
                                                      color: Theme.of(context)
                                                              .appBarTheme
                                                              .foregroundColor
                                                              ?.withValues(
                                                                alpha: 0.7,
                                                              ) ??
                                                          colorScheme
                                                              .onSurfaceVariant,
                                                      size: DesignSystem
                                                              .widgetIconSizeSmall +
                                                          4,
                                                    ),
                                                    onPressed: () {
                                                      _searchController.clear();
                                                      final bookshelfState =
                                                          _getBookshelfState();
                                                      if (bookshelfState !=
                                                          null) {
                                                        bookshelfState
                                                            .searchBooks(
                                                          null,
                                                        );
                                                      }
                                                    },
                                                  ),
                                              ],
                                              onChanged: (value) {
                                                final bookshelfState =
                                                    _getBookshelfState();
                                                if (bookshelfState != null) {
                                                  bookshelfState.searchBooks(
                                                    value.isEmpty
                                                        ? null
                                                        : value,
                                                  );
                                                }
                                              },
                                            ),
                                          )
                                        : Center(
                                            child: Text(
                                              'DassoShu',
                                              semanticsLabel:
                                                  'DassoShu Reader App',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .titleLarge
                                                  ?.copyWith(
                                                    fontWeight: DesignSystem
                                                        .getAdjustedFontWeight(
                                                      FontWeight.w400,
                                                      text: 'DassoShu',
                                                    ),
                                                    color: Theme.of(context)
                                                            .appBarTheme
                                                            .foregroundColor ??
                                                        colorScheme.onSurface,
                                                    letterSpacing: 0.3,
                                                  ),
                                            ),
                                          ),
                                  ),

                                  // This ensures balanced spacing on the right side
                                  if (_currentIndex == 0 && !_showSearch)
                                    IconButton(
                                      icon: Icon(
                                        AdaptiveIcons.search,
                                        color: Theme.of(context)
                                                .appBarTheme
                                                .foregroundColor ??
                                            colorScheme.onSurface,
                                      ),
                                      tooltip: 'Search books',
                                      constraints: DesignSystem
                                          .getMinTouchTargetConstraints(),
                                      onPressed: () {
                                        // Set up focus node before the state change
                                        final focusNode = FocusNode();
                                        setState(() {
                                          _showSearch = true;
                                        });

                                        // Schedule focus using proper pattern to avoid BuildContext across async gap
                                        WidgetsBinding.instance
                                            .addPostFrameCallback((_) {
                                          if (mounted) {
                                            focusNode.requestFocus();
                                          }
                                        });

                                        // Add haptic feedback for better accessibility
                                        HapticFeedback.selectionClick();
                                      },
                                    )
                                  else if (_showSearch)
                                    IconButton(
                                      icon: Icon(
                                        AdaptiveIcons.close,
                                        color: Theme.of(context)
                                                .appBarTheme
                                                .foregroundColor ??
                                            colorScheme.onSurface,
                                      ),
                                      tooltip: 'Clear search',
                                      constraints: DesignSystem
                                          .getMinTouchTargetConstraints(),
                                      onPressed: () {
                                        setState(() {
                                          _showSearch = false;
                                          _searchController.clear();
                                          if (_currentIndex == 0) {
                                            final bookshelfState =
                                                _getBookshelfState();
                                            if (bookshelfState != null) {
                                              bookshelfState.searchBooks(null);
                                            }
                                          }
                                        });
                                        // Add haptic feedback for better accessibility
                                        HapticFeedback.selectionClick();
                                      },
                                    )
                                  else
                                    // Add an invisible spacer of same width as the icon button
                                    // to balance the layout when no icon is showing
                                    const SizedBox(
                                      width: DesignSystem.widgetMinTouchTarget,
                                    ),
                                ],
                              ),
                            ),
                            // Minimal spacing between AppBar and TabBar
                            const SizedBox(height: DesignSystem.spaceS),
                          ],
                        ),
                      ),
                    ),
                    title:
                        null, // Remove original title since we moved it to flexibleSpace
                    actions: const [], // Keep empty since actions are in the row
                    bottom: PreferredSize(
                      preferredSize: Size.fromHeight(
                        DesignSystem.getAdaptiveTabHeight(context),
                      ), // Material 3 standard height
                      child: Container(
                        decoration: BoxDecoration(
                          color:
                              Theme.of(context).appBarTheme.backgroundColor ??
                                  colorScheme.surface,
                          border: Border(
                            bottom: BorderSide(
                              color: colorScheme.outlineVariant.withAlpha(128),
                              width: 1,
                            ),
                          ),
                        ),
                        child: TabBar(
                          controller: _tabController,
                          tabs: destinations.map((destination) {
                            final index = destinations.indexOf(destination);
                            final isSelected = index == _currentIndex;
                            return Tab(
                              height:
                                  DesignSystem.getAdaptiveTabHeight(context),
                              child: ResponsiveTab(
                                text: destination.getLabel(context),
                                icon: Icon(
                                  isSelected
                                      ? destination.getIcon(selected: true)
                                      : destination.icon,
                                ),
                                isSelected: isSelected,
                                colorScheme: colorScheme,
                                enableDebugLogging: kDebugMode,
                              ),
                            );
                          }).toList(),
                          // Fixed spacing approach for consistent visual separation across devices
                          isScrollable:
                              false, // Use fixed layout for consistent spacing
                          labelColor:
                              Theme.of(context).tabBarTheme.labelColor ??
                                  colorScheme.primary,
                          unselectedLabelColor: Theme.of(context)
                                  .tabBarTheme
                                  .unselectedLabelColor ??
                              colorScheme.onSurfaceVariant
                                  .withAlpha(204), // 0.8 * 255 = 204
                          indicatorColor:
                              Theme.of(context).tabBarTheme.indicatorColor ??
                                  colorScheme.primary,
                          indicatorWeight: 3.0,
                          indicator: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                color: Theme.of(context)
                                        .tabBarTheme
                                        .indicatorColor ??
                                    colorScheme.primary,
                                width: 3.0,
                              ),
                            ),
                          ),
                          // Fixed spacing configuration for identical visual gaps across all devices
                          padding: EdgeInsets.zero,
                          labelPadding: EdgeInsets.symmetric(
                            horizontal: _getManufacturerAwareTabSpacing(
                              context,
                            ),
                          ),
                          tabAlignment: TabAlignment
                              .fill, // Fill for consistent distribution
                          splashBorderRadius: BorderRadius.circular(
                            DesignSystem.radiusM,
                          ),
                          // MD3 compliant state layers using DesignSystem
                          overlayColor: DesignSystem.createStateLayerProperty(
                            colorScheme.primary,
                            includeSelected: false,
                            includeDragged: false,
                          ),
                          // Add haptic feedback for tab selection
                          onTap: (index) {
                            if (_currentIndex != index) {
                              nav.NavigationSystem.provideFeedback(
                                nav.NavigationFeedbackType.selection,
                              );
                              setState(() {
                                _currentIndex = index;

                                // Hide search when switching away from bookshelf tab
                                if (index != 0 && _showSearch) {
                                  _showSearch = false;
                                  _searchController.clear();
                                  final bookshelfState = _getBookshelfState();
                                  if (bookshelfState != null) {
                                    bookshelfState.searchBooks(null);
                                  }
                                }
                              });
                            }
                          },
                        ),
                      ),
                    ),
                  ),
                ];
              },
              // Use PageView for smooth page transitions
              body: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentIndex = index;

                    // Hide search when switching away from bookshelf tab
                    if (index != 0 && _showSearch) {
                      _showSearch = false;
                      _searchController.clear();
                      final bookshelfState = _getBookshelfState();
                      if (bookshelfState != null) {
                        bookshelfState.searchBooks(null);
                      }
                    }
                  });
                },
                children: pages,
              ),
            ),
            floatingActionButton: _currentIndex == 0
                ? Padding(
                    padding: const EdgeInsets.only(
                      bottom: DesignSystem.spaceM,
                      right: DesignSystem.spaceM,
                    ),
                    child: FloatingActionButton(
                      onPressed: () {
                        _showAddOptions();
                        // Add haptic feedback for better accessibility
                        HapticFeedback.mediumImpact();
                      },
                      elevation: DesignSystem.elevationM,
                      shape: const CircleBorder(),
                      // Add animation for FAB
                      heroTag: 'importBookFAB',
                      // Enhanced styling - use template colors if available
                      backgroundColor: Theme.of(context)
                              .floatingActionButtonTheme
                              .backgroundColor ??
                          colorScheme.primaryContainer,
                      foregroundColor: Theme.of(context)
                              .floatingActionButtonTheme
                              .foregroundColor ??
                          colorScheme.onPrimaryContainer,
                      // Add tooltip for accessibility
                      tooltip: 'Add Book',
                      child: Semantics(
                        label: 'Add new book',
                        hint: 'Opens menu to add books',
                        button: true,
                        child: Icon(
                          AdaptiveIcons.add,
                          size: DesignSystem.getAdjustedIconSize(
                            DesignSystem.widgetIconSizeMedium,
                          ),
                        ),
                      ),
                    ),
                  )
                : null,
          );
        }
      },
    );
  }

  // Now update the getBookshelfState method to use the GlobalKey
  BookshelfPageState? _getBookshelfState() {
    return _bookshelfKey.currentState;
  }
}

/// Profile detail pane widget for tablet landscape master-detail layout
/// Supports nested navigation within the profile section while maintaining
/// the side navigation menu visibility
class _ProfileDetailPane extends StatefulWidget {
  const _ProfileDetailPane({
    required this.onClose,
    required this.initialContent,
  });

  final VoidCallback onClose;
  final Widget initialContent;

  @override
  State<_ProfileDetailPane> createState() => _ProfileDetailPaneState();
}

class _ProfileDetailPaneState extends State<_ProfileDetailPane> {
  late final List<Widget> _navigationStack;
  late final List<String> _titleStack;

  // Performance monitoring
  TabletPerformanceUtils get _performanceUtils =>
      TabletPerformanceUtils.instance;

  @override
  void initState() {
    super.initState();
    _navigationStack = [widget.initialContent];
    _titleStack = ['Profile'];

    // Track initial profile pane render
    _performanceUtils.startComponentTracking('profilePaneInit');
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _performanceUtils.stopComponentTracking('profilePaneInit');
    });
  }

  void _pushPage(Widget page, String title) {
    // Track navigation performance within profile pane
    _performanceUtils.startComponentTracking('profileNavigation');

    setState(() {
      _navigationStack.add(page);
      _titleStack.add(title);
    });

    // Complete tracking after rebuild
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _performanceUtils.stopComponentTracking('profileNavigation');
    });
  }

  void _popPage() {
    if (_navigationStack.length > 1) {
      // Track back navigation performance
      _performanceUtils.startComponentTracking('profileBackNavigation');

      setState(() {
        _navigationStack.removeLast();
        _titleStack.removeLast();
      });

      // Complete tracking after rebuild
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _performanceUtils.stopComponentTracking('profileBackNavigation');
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Track profile pane render performance
    _performanceUtils.startComponentTracking('profilePaneRender');

    final currentPage = _navigationStack.last;
    final currentTitle = _titleStack.last;
    final canPop = _navigationStack.length > 1;

    // Wrap the current page to enable navigation callbacks
    // Use RepaintBoundary to isolate repaints for better performance
    Widget wrappedPage = RepaintBoundary(
      child: currentPage is SettingsPage
          ? SettingsPage(
              onNavigateToStats: _pushPage,
              onNavigateToMoreSettings: _pushPage,
            )
          : currentPage,
    );

    // Complete render tracking after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _performanceUtils.stopComponentTracking('profilePaneRender');
    });

    return Scaffold(
      appBar: AppBar(
        title: Text(currentTitle),
        leading: canPop
            ? IconButton(
                icon: Icon(AdaptiveIcons.back),
                onPressed: _popPage,
                tooltip: 'Back',
              )
            : IconButton(
                icon: Icon(AdaptiveIcons.back),
                onPressed: widget.onClose,
                tooltip: 'Close profile',
              ),
      ),
      body: wrappedPage,
    );
  }
}
