import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart' hide NavigationDestination;
import 'package:dasso_reader/config/navigation_system.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/responsive_system.dart';
import 'package:dasso_reader/config/app_icons.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/config/platform_adaptations.dart';
import 'package:dasso_reader/utils/performance/tablet_performance_utils.dart';
import 'package:dasso_reader/utils/state_management/rebuild_optimization.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:flutter/material.dart' as material;

/// Lazy loading manager for NavigationRail destinations
class NavigationRailLazyLoader {
  static final Map<String, NavigationRailDestination> _destinationCache = {};
  static final Map<String, bool> _loadingStates = {};

  /// Get or create a NavigationRail destination with lazy loading
  static NavigationRailDestination getDestination(
    String key,
    NavigationDestination destination,
    BuildContext context, {
    bool forceReload = false,
  }) {
    // Return cached destination if available and not forcing reload
    if (!forceReload && _destinationCache.containsKey(key)) {
      return _destinationCache[key]!;
    }

    // Check if already loading
    if (_loadingStates[key] == true) {
      return _createPlaceholderDestination(context);
    }

    // Mark as loading and create destination
    _loadingStates[key] = true;

    final railDestination = NavigationRailDestination(
      icon: Icon(destination.icon),
      selectedIcon: Icon(destination.getIcon(selected: true)),
      label: Text(destination.getLabel(context)),
    );

    // Cache the destination
    _destinationCache[key] = railDestination;
    _loadingStates[key] = false;

    return railDestination;
  }

  /// Create a placeholder destination for loading state
  static NavigationRailDestination _createPlaceholderDestination(
    BuildContext context,
  ) {
    return NavigationRailDestination(
      icon: Container(
        width: AppIcons.sizeM,
        height: AppIcons.sizeM,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(DesignSystem.radiusS),
        ),
      ),
      label: Container(
        width: 60,
        height: 12,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(DesignSystem.radiusS),
        ),
      ),
    );
  }

  /// Clear cache for specific destination
  static void clearDestination(String key) {
    _destinationCache.remove(key);
    _loadingStates.remove(key);
  }

  /// Clear all cached destinations
  static void clearAll() {
    _destinationCache.clear();
    _loadingStates.clear();
  }

  /// Get cache statistics for performance monitoring
  static Map<String, dynamic> getCacheStats() {
    return {
      'cachedDestinations': _destinationCache.length,
      'loadingDestinations':
          _loadingStates.values.where((loading) => loading).length,
      'cacheKeys': _destinationCache.keys.toList(),
    };
  }
}

/// Optimized search bar animation controller for NavigationRail
class SearchBarAnimationController extends ChangeNotifier {
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isExpanded = false;
  bool _isAnimating = false;

  SearchBarAnimationController({required TickerProvider vsync}) {
    _animationController = AnimationController(
      duration: DesignSystem.durationMedium,
      vsync: vsync,
    );

    // Optimized curves for smooth tablet animations
    _expandAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic, // Smooth expansion
        reverseCurve: Curves.easeInCubic, // Quick collapse
      ),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.3, 1.0, curve: Curves.easeOut),
        reverseCurve: const Interval(0.0, 0.7, curve: Curves.easeIn),
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(-0.2, 0.0),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutBack,
        reverseCurve: Curves.easeInBack,
      ),
    );

    // Listen for animation status changes
    _animationController.addStatusListener(_onAnimationStatusChanged);
  }

  // Getters
  bool get isExpanded => _isExpanded;
  bool get isAnimating => _isAnimating;
  Animation<double> get expandAnimation => _expandAnimation;
  Animation<double> get fadeAnimation => _fadeAnimation;
  Animation<Offset> get slideAnimation => _slideAnimation;

  /// Expand search bar with optimized animation
  Future<void> expand() async {
    if (_isExpanded || _isAnimating) return;

    _isAnimating = true;
    notifyListeners();

    // Start performance tracking
    TabletPerformanceUtils.instance.startSearchBarAnimationProfiling(
      'navigation_rail_search',
      'expand',
    );

    try {
      await _animationController.forward();
      _isExpanded = true;
    } catch (e) {
      AnxLog.warning('Search bar expand animation failed: $e');
    } finally {
      _isAnimating = false;
      notifyListeners();

      // End performance tracking
      TabletPerformanceUtils.instance.endSearchBarAnimationProfiling(
        'navigation_rail_search',
        'expand',
        wasSmooth: _animationController.status == AnimationStatus.completed,
      );
    }
  }

  /// Collapse search bar with optimized animation
  Future<void> collapse() async {
    if (!_isExpanded || _isAnimating) return;

    _isAnimating = true;
    notifyListeners();

    // Start performance tracking
    TabletPerformanceUtils.instance.startSearchBarAnimationProfiling(
      'navigation_rail_search',
      'collapse',
    );

    try {
      await _animationController.reverse();
      _isExpanded = false;
    } catch (e) {
      AnxLog.warning('Search bar collapse animation failed: $e');
    } finally {
      _isAnimating = false;
      notifyListeners();

      // End performance tracking
      TabletPerformanceUtils.instance.endSearchBarAnimationProfiling(
        'navigation_rail_search',
        'collapse',
        wasSmooth: _animationController.status == AnimationStatus.dismissed,
      );
    }
  }

  /// Toggle search bar state
  Future<void> toggle() async {
    if (_isExpanded) {
      await collapse();
    } else {
      await expand();
    }
  }

  void _onAnimationStatusChanged(AnimationStatus status) {
    // Track animation performance impact on NavigationRail
    if (status == AnimationStatus.completed ||
        status == AnimationStatus.dismissed) {
      TabletPerformanceUtils.instance.trackSearchBarImpactOnNavigationRail(
        'enhanced_navigation_rail',
        searchBarVisible: _isExpanded,
        isAnimating: false,
        lastAnimationDuration: DesignSystem.durationMedium,
      );
    }
  }

  @override
  void dispose() {
    _animationController.removeStatusListener(_onAnimationStatusChanged);
    _animationController.dispose();
    super.dispose();
  }
}

/// Intelligent caching system for side menu components
class SideMenuComponentCache {
  static final Map<String, Widget> _widgetCache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};
  static final Map<String, int> _accessCounts = {};

  // Cache configuration
  static const Duration _cacheExpiry = Duration(minutes: 30);
  static const int _maxCacheSize = 50;
  static const int _accessThreshold = 3; // Cache widgets accessed 3+ times

  /// Get or create a cached widget
  static Widget getOrCache(
    String key,
    Widget Function() widgetBuilder, {
    bool forceRefresh = false,
    Duration? customExpiry,
  }) {
    final now = DateTime.now();
    final expiry = customExpiry ?? _cacheExpiry;

    // Check if we should use cached version
    if (!forceRefresh && _widgetCache.containsKey(key)) {
      final timestamp = _cacheTimestamps[key];
      if (timestamp != null && now.difference(timestamp) < expiry) {
        // Update access count and return cached widget
        _accessCounts[key] = (_accessCounts[key] ?? 0) + 1;

        if (kDebugMode) {
          AnxLog.info(
            '📦 Cache hit for widget: $key (${_accessCounts[key]} accesses)',
          );
        }

        return _widgetCache[key]!;
      } else {
        // Cache expired, remove it
        _removeFromCache(key);
      }
    }

    // Build new widget
    final widget = widgetBuilder();

    // Decide whether to cache based on access pattern
    final accessCount = _accessCounts[key] ?? 0;
    if (accessCount >= _accessThreshold || forceRefresh) {
      _addToCache(key, widget, now);
    } else {
      // Just increment access count for future caching decision
      _accessCounts[key] = accessCount + 1;
    }

    return widget;
  }

  /// Add widget to cache with size management
  static void _addToCache(String key, Widget widget, DateTime timestamp) {
    // Manage cache size
    if (_widgetCache.length >= _maxCacheSize) {
      _evictLeastUsed();
    }

    _widgetCache[key] = widget;
    _cacheTimestamps[key] = timestamp;

    if (kDebugMode) {
      AnxLog.info(
        '📦 Cached widget: $key (cache size: ${_widgetCache.length})',
      );
    }
  }

  /// Remove widget from cache
  static void _removeFromCache(String key) {
    _widgetCache.remove(key);
    _cacheTimestamps.remove(key);

    if (kDebugMode) {
      AnxLog.info('📦 Removed from cache: $key');
    }
  }

  /// Evict least used widgets when cache is full
  static void _evictLeastUsed() {
    if (_accessCounts.isEmpty) return;

    // Find the least accessed widget
    String? leastUsedKey;
    int minAccess = double.maxFinite.toInt();

    for (final entry in _accessCounts.entries) {
      if (_widgetCache.containsKey(entry.key) && entry.value < minAccess) {
        minAccess = entry.value;
        leastUsedKey = entry.key;
      }
    }

    if (leastUsedKey != null) {
      _removeFromCache(leastUsedKey);
      _accessCounts.remove(leastUsedKey);

      if (kDebugMode) {
        AnxLog.info(
          '📦 Evicted least used widget: $leastUsedKey ($minAccess accesses)',
        );
      }
    }
  }

  /// Clear expired cache entries
  static void clearExpired() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) > _cacheExpiry) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _removeFromCache(key);
    }

    if (kDebugMode && expiredKeys.isNotEmpty) {
      AnxLog.info('📦 Cleared ${expiredKeys.length} expired cache entries');
    }
  }

  /// Clear all cache entries
  static void clearAll() {
    final cacheSize = _widgetCache.length;
    _widgetCache.clear();
    _cacheTimestamps.clear();
    _accessCounts.clear();

    if (kDebugMode) {
      AnxLog.info('📦 Cleared all cache entries ($cacheSize widgets)');
    }
  }

  /// Get cache statistics
  static Map<String, dynamic> getStats() {
    final now = DateTime.now();
    int expiredCount = 0;

    for (final timestamp in _cacheTimestamps.values) {
      if (now.difference(timestamp) > _cacheExpiry) {
        expiredCount++;
      }
    }

    return {
      'totalCached': _widgetCache.length,
      'expiredEntries': expiredCount,
      'totalAccesses':
          _accessCounts.values.fold(0, (sum, count) => sum + count),
      'averageAccesses': _accessCounts.isNotEmpty
          ? _accessCounts.values.reduce((a, b) => a + b) / _accessCounts.length
          : 0.0,
      'cacheKeys': _widgetCache.keys.toList(),
    };
  }

  /// Preload commonly used widgets
  static void preloadCommonWidgets(BuildContext context) {
    // Preload common navigation icons
    final commonIcons = [
      'bookshelf_icon',
      'dictionary_icon',
      'vocabulary_icon',
      'hsk_icon',
      'notes_icon',
    ];

    for (final iconKey in commonIcons) {
      getOrCache(
        iconKey,
        () => Icon(
          AdaptiveIcons.bookshelf,
        ), // Placeholder - would be actual icons
        forceRefresh: false,
      );
    }

    if (kDebugMode) {
      AnxLog.info('📦 Preloaded ${commonIcons.length} common widgets');
    }
  }
}

/// RepaintBoundary optimization utilities for NavigationRail components
class NavigationRailRepaintOptimizer {
  /// Wrap widget with RepaintBoundary if it meets optimization criteria
  static Widget optimizeWidget(
    Widget child, {
    required String componentType,
    required bool isExpensive,
    required bool changesFrequently,
  }) {
    // Only wrap with RepaintBoundary if the widget is expensive to render
    // but doesn't change frequently
    if (isExpensive && !changesFrequently) {
      if (kDebugMode) {
        AnxLog.info('🎨 Optimizing $componentType with RepaintBoundary');
      }
      return RepaintBoundary(child: child);
    }

    return child;
  }

  /// Optimize NavigationRail leading component (search bar, avatar, etc.)
  static Widget optimizeLeading(Widget leading) {
    return optimizeWidget(
      leading,
      componentType: 'NavigationRail Leading',
      isExpensive: true, // Search bars and avatars are expensive
      changesFrequently: false, // Leading components don't change often
    );
  }

  /// Optimize NavigationRail trailing component
  static Widget optimizeTrailing(Widget trailing) {
    return optimizeWidget(
      trailing,
      componentType: 'NavigationRail Trailing',
      isExpensive: true, // Trailing components often contain complex widgets
      changesFrequently: false, // Trailing components are usually static
    );
  }

  /// Optimize NavigationRail destination icon
  static Widget optimizeDestinationIcon(
    Widget icon, {
    required bool isSelected,
  }) {
    return optimizeWidget(
      icon,
      componentType: 'Destination Icon',
      isExpensive: true, // Icons can be expensive with custom graphics
      changesFrequently: isSelected, // Selected icons change with navigation
    );
  }

  /// Optimize NavigationRail destination label
  static Widget optimizeDestinationLabel(Widget label) {
    return optimizeWidget(
      label,
      componentType: 'Destination Label',
      isExpensive: false, // Text labels are usually not expensive
      changesFrequently: false, // Labels don't change frequently
    );
  }

  /// Get RepaintBoundary optimization statistics
  static Map<String, dynamic> getOptimizationStats() {
    // This would track how many components have been optimized
    // For now, return placeholder stats
    return {
      'optimizedComponents': 0,
      'skippedComponents': 0,
      'optimizationRatio': 0.0,
    };
  }
}

/// Validation system for side menu layout optimizations
class SideMenuOptimizationValidator {
  static final Map<String, List<Duration>> _performanceHistory = {};
  static final Map<String, int> _validationCounts = {};

  /// Validate NavigationRail performance improvements
  static Future<Map<String, dynamic>> validateNavigationRailOptimizations({
    required String railId,
    required BuildContext context,
  }) async {
    final results = <String, dynamic>{};

    try {
      // Test 1: Cross-platform compatibility check (before async operations)
      final compatibilityResult = _validateCrossPlatformCompatibility(context);
      results['crossPlatform'] = compatibilityResult;

      // Test 2: Measure render time
      final renderTimeResult = await _measureRenderTime(railId, context);
      results['renderTime'] = renderTimeResult;

      // Test 3: Validate lazy loading efficiency
      final lazyLoadingResult = _validateLazyLoading();
      results['lazyLoading'] = lazyLoadingResult;

      // Test 4: Check component caching effectiveness
      final cachingResult = _validateComponentCaching();
      results['componentCaching'] = cachingResult;

      // Test 5: Verify RepaintBoundary optimization
      final repaintResult = _validateRepaintBoundaryOptimization();
      results['repaintBoundary'] = repaintResult;

      // Calculate overall optimization score
      final overallScore = _calculateOptimizationScore(results);
      results['overallScore'] = overallScore;
      results['validationTime'] = DateTime.now().toIso8601String();

      // Log validation results
      if (kDebugMode) {
        _logValidationResults(railId, results);
      }
    } catch (e) {
      results['error'] = e.toString();
      AnxLog.severe('Side menu optimization validation failed: $e');
    }

    return results;
  }

  /// Measure NavigationRail render time performance
  static Future<Map<String, dynamic>> _measureRenderTime(
    String railId,
    BuildContext context,
  ) async {
    final stopwatch = Stopwatch()..start();

    // Simulate NavigationRail rebuild
    await Future<void>.delayed(const Duration(milliseconds: 1));

    stopwatch.stop();
    final renderTime = stopwatch.elapsed;

    // Store in performance history
    _performanceHistory.putIfAbsent(railId, () => <Duration>[]);
    _performanceHistory[railId]!.add(renderTime);

    // Keep only recent measurements
    if (_performanceHistory[railId]!.length > 20) {
      _performanceHistory[railId]!.removeAt(0);
    }

    // Calculate performance metrics
    final history = _performanceHistory[railId]!;
    final avgRenderTime = history.isNotEmpty
        ? history.map((d) => d.inMicroseconds).reduce((a, b) => a + b) /
            history.length
        : 0.0;

    final isOptimal = avgRenderTime < 16000; // 16ms for 60fps

    return {
      'currentRenderTime': renderTime.inMicroseconds / 1000,
      'averageRenderTime': avgRenderTime / 1000,
      'isOptimal': isOptimal,
      'targetRenderTime': 16.0,
      'measurementCount': history.length,
    };
  }

  /// Validate lazy loading efficiency
  static Map<String, dynamic> _validateLazyLoading() {
    final stats = NavigationRailLazyLoader.getCacheStats();
    final cachedCount = stats['cachedDestinations'] as int;
    final loadingCount = stats['loadingDestinations'] as int;

    final efficiency =
        cachedCount > 0 ? cachedCount / (cachedCount + loadingCount) : 0.0;
    final isEfficient = efficiency > 0.7; // 70% cache hit rate target

    return {
      'cachedDestinations': cachedCount,
      'loadingDestinations': loadingCount,
      'cacheEfficiency': efficiency,
      'isEfficient': isEfficient,
      'targetEfficiency': 0.7,
    };
  }

  /// Validate component caching effectiveness
  static Map<String, dynamic> _validateComponentCaching() {
    final stats = SideMenuComponentCache.getStats();
    final totalCached = stats['totalCached'] as int;
    final avgAccesses = stats['averageAccesses'] as double;

    final isEffective = totalCached > 0 && avgAccesses > 2.0;

    return {
      'totalCachedComponents': totalCached,
      'averageAccesses': avgAccesses,
      'isEffective': isEffective,
      'targetAccesses': 2.0,
    };
  }

  /// Validate RepaintBoundary optimization
  static Map<String, dynamic> _validateRepaintBoundaryOptimization() {
    final stats = NavigationRailRepaintOptimizer.getOptimizationStats();
    final optimizedComponents = stats['optimizedComponents'] as int;
    final optimizationRatio = stats['optimizationRatio'] as double;

    final isOptimized = optimizationRatio > 0.5; // 50% optimization target

    return {
      'optimizedComponents': optimizedComponents,
      'optimizationRatio': optimizationRatio,
      'isOptimized': isOptimized,
      'targetRatio': 0.5,
    };
  }

  /// Validate cross-platform compatibility
  static Map<String, dynamic> _validateCrossPlatformCompatibility(
    BuildContext context,
  ) {
    final isTablet = DesignSystem.isTablet(context);
    final isLandscape =
        ResponsiveSystem.getOrientation(context) == Orientation.landscape;
    final isIOS = PlatformAdaptations.isIOS;

    // Check if optimizations are properly applied for current platform
    final isCompatible = isTablet &&
        isLandscape; // Optimizations should only apply to tablet landscape

    return {
      'isTablet': isTablet,
      'isLandscape': isLandscape,
      'isIOS': isIOS,
      'isCompatible': isCompatible,
      'shouldOptimize': isTablet && isLandscape,
    };
  }

  /// Calculate overall optimization score (0-100)
  static double _calculateOptimizationScore(Map<String, dynamic> results) {
    double score = 0.0;
    int testCount = 0;

    // Render time score (25 points)
    final renderTime = results['renderTime'] as Map<String, dynamic>?;
    if (renderTime != null && renderTime['isOptimal'] == true) {
      score += 25.0;
    }
    testCount++;

    // Lazy loading score (25 points)
    final lazyLoading = results['lazyLoading'] as Map<String, dynamic>?;
    if (lazyLoading != null && lazyLoading['isEfficient'] == true) {
      score += 25.0;
    }
    testCount++;

    // Component caching score (25 points)
    final caching = results['componentCaching'] as Map<String, dynamic>?;
    if (caching != null && caching['isEffective'] == true) {
      score += 25.0;
    }
    testCount++;

    // RepaintBoundary score (25 points)
    final repaint = results['repaintBoundary'] as Map<String, dynamic>?;
    if (repaint != null && repaint['isOptimized'] == true) {
      score += 25.0;
    }
    testCount++;

    return testCount > 0 ? score : 0.0;
  }

  /// Log validation results for debugging
  static void _logValidationResults(
    String railId,
    Map<String, dynamic> results,
  ) {
    final score = results['overallScore'] as double;

    AnxLog.info('🔍 Side Menu Optimization Validation Results ($railId):');
    AnxLog.info('   Overall Score: ${score.toStringAsFixed(1)}/100');

    final renderTime = results['renderTime'] as Map<String, dynamic>?;
    if (renderTime != null) {
      AnxLog.info(
        '   Render Time: ${renderTime['currentRenderTime']}ms (optimal: ${renderTime['isOptimal']})',
      );
    }

    final lazyLoading = results['lazyLoading'] as Map<String, dynamic>?;
    if (lazyLoading != null) {
      final efficiency = (lazyLoading['cacheEfficiency'] as double) * 100;
      AnxLog.info(
        '   Lazy Loading: ${efficiency.toStringAsFixed(1)}% efficiency',
      );
    }

    final caching = results['componentCaching'] as Map<String, dynamic>?;
    if (caching != null) {
      AnxLog.info(
        '   Component Caching: ${caching['totalCachedComponents']} cached components',
      );
    }

    final repaint = results['repaintBoundary'] as Map<String, dynamic>?;
    if (repaint != null) {
      AnxLog.info(
        '   RepaintBoundary: ${repaint['optimizedComponents']} optimized components',
      );
    }

    if (score >= 80) {
      AnxLog.info('   ✅ Excellent optimization performance!');
    } else if (score >= 60) {
      AnxLog.info('   ⚠️ Good optimization, room for improvement');
    } else {
      AnxLog.warning('   ❌ Optimization needs attention');
    }
  }

  /// Get validation history for performance tracking
  static Map<String, dynamic> getValidationHistory(String railId) {
    final history = _performanceHistory[railId] ?? [];
    final validationCount = _validationCounts[railId] ?? 0;

    return {
      'railId': railId,
      'performanceHistory':
          history.map((d) => d.inMicroseconds / 1000).toList(),
      'validationCount': validationCount,
      'lastValidation':
          history.isNotEmpty ? history.last.inMicroseconds / 1000 : null,
    };
  }

  /// Clear validation history
  static void clearValidationHistory([String? railId]) {
    if (railId != null) {
      _performanceHistory.remove(railId);
      _validationCounts.remove(railId);
    } else {
      _performanceHistory.clear();
      _validationCounts.clear();
    }
  }
}

/// Advanced RepaintBoundary management system for NavigationRail optimization
class AdvancedRepaintBoundaryManager {
  static final Map<String, RepaintBoundaryMetrics> _componentMetrics = {};
  static final Map<String, bool> _boundaryStates = {};
  static final Map<String, DateTime> _lastOptimizationTimes = {};

  // Performance thresholds for dynamic boundary management
  static const Duration _optimizationInterval = Duration(seconds: 30);
  static const int _rebuildThreshold = 5; // Rebuilds per optimization interval
  static const Duration _renderTimeThreshold =
      Duration(milliseconds: 16); // 60fps target
  static const double _frameDropThreshold = 0.1; // 10% frame drop tolerance

  /// Record performance metrics for a NavigationRail component
  static void recordComponentMetrics(
    String componentId, {
    required Duration renderTime,
    required int rebuildCount,
    required bool hadFrameDrops,
    Map<String, dynamic>? additionalData,
  }) {
    final now = DateTime.now();

    // Initialize or update metrics
    _componentMetrics.putIfAbsent(
      componentId,
      () => RepaintBoundaryMetrics(componentId),
    );

    final metrics = _componentMetrics[componentId]!;
    metrics.addMeasurement(
      renderTime: renderTime,
      rebuildCount: rebuildCount,
      hadFrameDrops: hadFrameDrops,
      timestamp: now,
      additionalData: additionalData,
    );

    // Check if optimization is needed
    _checkOptimizationNeeded(componentId, now);
  }

  /// Check if RepaintBoundary optimization is needed for a component
  static void _checkOptimizationNeeded(String componentId, DateTime now) {
    final lastOptimization = _lastOptimizationTimes[componentId];

    // Only optimize at intervals to avoid thrashing
    if (lastOptimization != null &&
        now.difference(lastOptimization) < _optimizationInterval) {
      return;
    }

    final metrics = _componentMetrics[componentId];
    if (metrics == null) return;

    final analysis = metrics.getPerformanceAnalysis();
    final shouldOptimize = _shouldOptimizeComponent(analysis);

    if (shouldOptimize != _boundaryStates[componentId]) {
      _boundaryStates[componentId] = shouldOptimize;
      _lastOptimizationTimes[componentId] = now;

      if (kDebugMode) {
        AnxLog.info(
          '🎨 RepaintBoundary optimization changed for $componentId: $shouldOptimize\n'
          '   Avg render time: ${analysis['averageRenderTime']}ms\n'
          '   Rebuild rate: ${analysis['rebuildRate']}/interval\n'
          '   Frame drop rate: ${analysis['frameDropRate']}%',
        );
      }
    }
  }

  /// Determine if a component should have RepaintBoundary optimization
  static bool _shouldOptimizeComponent(Map<String, dynamic> analysis) {
    final avgRenderTime = analysis['averageRenderTime'] as double? ?? 0.0;
    final rebuildRate = analysis['rebuildRate'] as double? ?? 0.0;
    final frameDropRate = analysis['frameDropRate'] as double? ?? 0.0;

    // Optimize if component is expensive to render but stable
    final isExpensive =
        avgRenderTime > _renderTimeThreshold.inMicroseconds / 1000;
    final isStable = rebuildRate < _rebuildThreshold;
    final hasFrameIssues = frameDropRate > _frameDropThreshold;

    // Apply RepaintBoundary if expensive and stable, or if having frame issues
    return (isExpensive && isStable) || hasFrameIssues;
  }

  /// Get current RepaintBoundary state for a component
  static bool shouldUseRepaintBoundary(String componentId) {
    return _boundaryStates[componentId] ?? false;
  }

  /// Wrap widget with RepaintBoundary if optimization is enabled
  static Widget optimizeWidget(String componentId, Widget child) {
    if (shouldUseRepaintBoundary(componentId)) {
      return RepaintBoundary(
        key: ValueKey('repaint_boundary_$componentId'),
        child: child,
      );
    }
    return child;
  }

  /// Get performance statistics for all managed components
  static Map<String, dynamic> getOptimizationStats() {
    final stats = <String, dynamic>{
      'totalComponents': _componentMetrics.length,
      'optimizedComponents': _boundaryStates.values.where((v) => v).length,
      'componentDetails': <String, dynamic>{},
    };

    for (final entry in _componentMetrics.entries) {
      final componentId = entry.key;
      final metrics = entry.value;
      final analysis = metrics.getPerformanceAnalysis();

      stats['componentDetails'][componentId] = {
        'isOptimized': _boundaryStates[componentId] ?? false,
        'averageRenderTime': analysis['averageRenderTime'],
        'rebuildRate': analysis['rebuildRate'],
        'frameDropRate': analysis['frameDropRate'],
        'measurementCount': analysis['measurementCount'],
      };
    }

    return stats;
  }

  /// Clear optimization data for a specific component or all components
  static void clearOptimizationData([String? componentId]) {
    if (componentId != null) {
      _componentMetrics.remove(componentId);
      _boundaryStates.remove(componentId);
      _lastOptimizationTimes.remove(componentId);
    } else {
      _componentMetrics.clear();
      _boundaryStates.clear();
      _lastOptimizationTimes.clear();
    }
  }
}

/// Metrics tracking for RepaintBoundary optimization decisions
class RepaintBoundaryMetrics {
  final String componentId;
  final List<PerformanceMeasurement> _measurements = [];

  RepaintBoundaryMetrics(this.componentId);

  /// Add a new performance measurement
  void addMeasurement({
    required Duration renderTime,
    required int rebuildCount,
    required bool hadFrameDrops,
    required DateTime timestamp,
    Map<String, dynamic>? additionalData,
  }) {
    _measurements.add(
      PerformanceMeasurement(
        renderTime: renderTime,
        rebuildCount: rebuildCount,
        hadFrameDrops: hadFrameDrops,
        timestamp: timestamp,
        additionalData: additionalData ?? {},
      ),
    );

    // Keep only recent measurements (last 50)
    if (_measurements.length > 50) {
      _measurements.removeAt(0);
    }
  }

  /// Get comprehensive performance analysis
  Map<String, dynamic> getPerformanceAnalysis() {
    if (_measurements.isEmpty) {
      return {
        'averageRenderTime': 0.0,
        'rebuildRate': 0.0,
        'frameDropRate': 0.0,
        'measurementCount': 0,
      };
    }

    // Calculate average render time
    final totalRenderTime = _measurements
        .map((m) => m.renderTime.inMicroseconds)
        .reduce((a, b) => a + b);
    final avgRenderTime =
        totalRenderTime / _measurements.length / 1000; // Convert to ms

    // Calculate rebuild rate (rebuilds per optimization interval)
    final now = DateTime.now();
    final recentMeasurements = _measurements.where(
      (m) =>
          now.difference(m.timestamp) <
          AdvancedRepaintBoundaryManager._optimizationInterval,
    );
    final rebuildRate = recentMeasurements.length.toDouble();

    // Calculate frame drop rate
    final frameDropCount = _measurements.where((m) => m.hadFrameDrops).length;
    final frameDropRate = frameDropCount / _measurements.length;

    return {
      'averageRenderTime': avgRenderTime,
      'rebuildRate': rebuildRate,
      'frameDropRate': frameDropRate,
      'measurementCount': _measurements.length,
      'componentId': componentId,
    };
  }
}

/// Individual performance measurement data
class PerformanceMeasurement {
  final Duration renderTime;
  final int rebuildCount;
  final bool hadFrameDrops;
  final DateTime timestamp;
  final Map<String, dynamic> additionalData;

  PerformanceMeasurement({
    required this.renderTime,
    required this.rebuildCount,
    required this.hadFrameDrops,
    required this.timestamp,
    required this.additionalData,
  });
}

/// Widget rebuild reduction system for NavigationRail optimization
class NavigationRailRebuildReducer {
  static final Map<String, Widget> _memoizedWidgets = {};
  static final Map<String, int> _widgetHashes = {};
  static final Map<String, DateTime> _lastMemoizationTimes = {};

  // Memoization configuration
  static const Duration _memoizationExpiry = Duration(minutes: 10);
  static const int _maxMemoizedWidgets = 100;

  /// Memoize a widget to prevent unnecessary rebuilds
  static Widget memoizeWidget(
    String key,
    Widget Function() widgetBuilder, {
    List<Object?>? dependencies,
    Duration? customExpiry,
  }) {
    final now = DateTime.now();
    final expiry = customExpiry ?? _memoizationExpiry;

    // Calculate hash of dependencies
    final dependencyHash = _calculateDependencyHash(dependencies);

    // Check if we have a valid cached widget
    final lastMemoization = _lastMemoizationTimes[key];
    final cachedHash = _widgetHashes[key];

    if (_memoizedWidgets.containsKey(key) &&
        lastMemoization != null &&
        now.difference(lastMemoization) < expiry &&
        cachedHash == dependencyHash) {
      if (kDebugMode) {
        AnxLog.info('🔄 Widget memoization hit: $key');
      }

      return _memoizedWidgets[key]!;
    }

    // Build new widget and cache it
    final widget = widgetBuilder();
    _addToMemoizationCache(key, widget, dependencyHash, now);

    if (kDebugMode) {
      AnxLog.info('🔄 Widget memoization miss: $key (rebuilding)');
    }

    return widget;
  }

  /// Add widget to memoization cache with size management
  static void _addToMemoizationCache(
    String key,
    Widget widget,
    int dependencyHash,
    DateTime timestamp,
  ) {
    // Manage cache size
    if (_memoizedWidgets.length >= _maxMemoizedWidgets) {
      _evictOldestMemoizedWidget();
    }

    _memoizedWidgets[key] = widget;
    _widgetHashes[key] = dependencyHash;
    _lastMemoizationTimes[key] = timestamp;
  }

  /// Evict oldest memoized widget when cache is full
  static void _evictOldestMemoizedWidget() {
    if (_lastMemoizationTimes.isEmpty) return;

    // Find the oldest entry
    String? oldestKey;
    DateTime? oldestTime;

    for (final entry in _lastMemoizationTimes.entries) {
      if (oldestTime == null || entry.value.isBefore(oldestTime)) {
        oldestTime = entry.value;
        oldestKey = entry.key;
      }
    }

    if (oldestKey != null) {
      _memoizedWidgets.remove(oldestKey);
      _widgetHashes.remove(oldestKey);
      _lastMemoizationTimes.remove(oldestKey);

      if (kDebugMode) {
        AnxLog.info('🔄 Evicted oldest memoized widget: $oldestKey');
      }
    }
  }

  /// Calculate hash of dependencies for memoization
  static int _calculateDependencyHash(List<Object?>? dependencies) {
    if (dependencies == null || dependencies.isEmpty) {
      return 0;
    }

    return Object.hashAll(dependencies);
  }

  /// Clear expired memoized widgets
  static void clearExpiredMemoizations() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _lastMemoizationTimes.entries) {
      if (now.difference(entry.value) > _memoizationExpiry) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _memoizedWidgets.remove(key);
      _widgetHashes.remove(key);
      _lastMemoizationTimes.remove(key);
    }

    if (kDebugMode && expiredKeys.isNotEmpty) {
      AnxLog.info('🔄 Cleared ${expiredKeys.length} expired memoized widgets');
    }
  }

  /// Get memoization statistics
  static Map<String, dynamic> getMemoizationStats() {
    final now = DateTime.now();
    int expiredCount = 0;

    for (final timestamp in _lastMemoizationTimes.values) {
      if (now.difference(timestamp) > _memoizationExpiry) {
        expiredCount++;
      }
    }

    return {
      'totalMemoized': _memoizedWidgets.length,
      'expiredEntries': expiredCount,
      'cacheUtilization': _memoizedWidgets.length / _maxMemoizedWidgets,
      'memoizedKeys': _memoizedWidgets.keys.toList(),
    };
  }

  /// Clear all memoization data
  static void clearAllMemoizations() {
    final count = _memoizedWidgets.length;
    _memoizedWidgets.clear();
    _widgetHashes.clear();
    _lastMemoizationTimes.clear();

    if (kDebugMode) {
      AnxLog.info('🔄 Cleared all memoized widgets ($count widgets)');
    }
  }
}

/// NavigationRail state management optimization system
class NavigationRailStateManager {
  static final Map<String, NavigationRailState> _stateCache = {};
  static final Map<String, List<VoidCallback>> _stateListeners = {};
  static final Map<String, DateTime> _lastStateChanges = {};

  // State change throttling configuration
  static const Duration _stateChangeThrottle =
      Duration(milliseconds: 16); // 60fps
  static const Duration _stateValidityDuration = Duration(seconds: 30);

  /// Get or create NavigationRail state
  static NavigationRailState getState(String railId) {
    return _stateCache.putIfAbsent(
      railId,
      () => NavigationRailState(railId: railId),
    );
  }

  /// Update NavigationRail state with throttling
  static void updateState(
    String railId, {
    int? currentIndex,
    bool? extended,
    List<NavigationDestination>? destinations,
    Widget? leading,
    Widget? trailing,
    bool? isAnimating,
    Map<String, dynamic>? additionalData,
  }) {
    final now = DateTime.now();
    final lastChange = _lastStateChanges[railId];

    // Throttle rapid state changes
    if (lastChange != null &&
        now.difference(lastChange) < _stateChangeThrottle) {
      if (kDebugMode) {
        AnxLog.info('🔄 NavigationRail state change throttled: $railId');
      }
      return;
    }

    final state = getState(railId);
    bool hasChanges = false;

    // Update state properties if they've changed
    if (currentIndex != null && state.currentIndex != currentIndex) {
      state._currentIndex = currentIndex;
      hasChanges = true;
    }

    if (extended != null && state.extended != extended) {
      state._extended = extended;
      hasChanges = true;
    }

    if (destinations != null &&
        !_areDestinationsEqual(state.destinations, destinations)) {
      state._destinations = List.from(destinations);
      hasChanges = true;
    }

    if (leading != state.leading) {
      state._leading = leading;
      hasChanges = true;
    }

    if (trailing != state.trailing) {
      state._trailing = trailing;
      hasChanges = true;
    }

    if (isAnimating != null && state.isAnimating != isAnimating) {
      state._isAnimating = isAnimating;
      hasChanges = true;
    }

    if (additionalData != null) {
      state._additionalData.addAll(additionalData);
      hasChanges = true;
    }

    // Only notify listeners if there are actual changes
    if (hasChanges) {
      state._lastUpdated = now;
      _lastStateChanges[railId] = now;
      _notifyStateListeners(railId);

      if (kDebugMode) {
        AnxLog.info('🔄 NavigationRail state updated: $railId');
      }
    }
  }

  /// Add state change listener
  static void addStateListener(String railId, VoidCallback listener) {
    _stateListeners.putIfAbsent(railId, () => <VoidCallback>[]);
    _stateListeners[railId]!.add(listener);
  }

  /// Remove state change listener
  static void removeStateListener(String railId, VoidCallback listener) {
    _stateListeners[railId]?.remove(listener);
  }

  /// Notify all listeners for a specific rail
  static void _notifyStateListeners(String railId) {
    final listeners = _stateListeners[railId];
    if (listeners != null) {
      for (final listener in listeners) {
        try {
          listener();
        } catch (e) {
          if (kDebugMode) {
            AnxLog.warning('🔄 NavigationRail state listener error: $e');
          }
        }
      }
    }
  }

  /// Check if destinations are equal (shallow comparison)
  static bool _areDestinationsEqual(
    List<NavigationDestination>? a,
    List<NavigationDestination>? b,
  ) {
    if (a == null && b == null) return true;
    if (a == null || b == null) return false;
    if (a.length != b.length) return false;

    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }

    return true;
  }

  /// Clear expired state cache entries
  static void clearExpiredStates() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _stateCache.entries) {
      if (now.difference(entry.value.lastUpdated) > _stateValidityDuration) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _stateCache.remove(key);
      _stateListeners.remove(key);
      _lastStateChanges.remove(key);
    }

    if (kDebugMode && expiredKeys.isNotEmpty) {
      AnxLog.info(
        '🔄 Cleared ${expiredKeys.length} expired NavigationRail states',
      );
    }
  }

  /// Get state management statistics
  static Map<String, dynamic> getStateStats() {
    final now = DateTime.now();
    int activeStates = 0;
    int totalListeners = 0;

    for (final entry in _stateCache.entries) {
      if (now.difference(entry.value.lastUpdated) < _stateValidityDuration) {
        activeStates++;
      }
    }

    for (final listeners in _stateListeners.values) {
      totalListeners += listeners.length;
    }

    return {
      'totalStates': _stateCache.length,
      'activeStates': activeStates,
      'totalListeners': totalListeners,
      'averageListenersPerState': _stateListeners.isNotEmpty
          ? totalListeners / _stateListeners.length
          : 0.0,
    };
  }

  /// Clear all state data
  static void clearAllStates() {
    final stateCount = _stateCache.length;
    _stateCache.clear();
    _stateListeners.clear();
    _lastStateChanges.clear();

    if (kDebugMode) {
      AnxLog.info('🔄 Cleared all NavigationRail states ($stateCount states)');
    }
  }
}

/// NavigationRail state data class
class NavigationRailState {
  final String railId;
  int _currentIndex = 0;
  bool _extended = false;
  List<NavigationDestination>? _destinations;
  Widget? _leading;
  Widget? _trailing;
  bool _isAnimating = false;
  DateTime _lastUpdated = DateTime.now();
  final Map<String, dynamic> _additionalData = {};

  NavigationRailState({required this.railId});

  // Getters
  int get currentIndex => _currentIndex;
  bool get extended => _extended;
  List<NavigationDestination>? get destinations => _destinations;
  Widget? get leading => _leading;
  Widget? get trailing => _trailing;
  bool get isAnimating => _isAnimating;
  DateTime get lastUpdated => _lastUpdated;
  Map<String, dynamic> get additionalData => Map.unmodifiable(_additionalData);

  /// Check if state has changed significantly
  bool hasSignificantChanges(NavigationRailState other) {
    return currentIndex != other.currentIndex ||
        extended != other.extended ||
        destinations?.length != other.destinations?.length ||
        isAnimating != other.isAnimating;
  }

  /// Get state hash for comparison
  int get stateHash {
    return Object.hash(
      currentIndex,
      extended,
      destinations?.length ?? 0,
      leading?.runtimeType,
      trailing?.runtimeType,
      isAnimating,
    );
  }
}

/// Widget tree optimization system for NavigationRail
class NavigationRailWidgetTreeOptimizer {
  static final Map<String, Widget> _optimizedWidgetCache = {};
  static final Map<String, int> _widgetComplexityScores = {};

  // Widget tree optimization thresholds
  static const int _maxNestingDepth = 5;
  static const int _complexityThreshold = 100;

  /// Optimize widget tree structure by flattening hierarchies
  static Widget optimizeWidgetTree(
    String key,
    Widget Function() widgetBuilder, {
    int? maxDepth,
    bool forceFlattening = false,
  }) {
    final effectiveMaxDepth = maxDepth ?? _maxNestingDepth;

    // Check cache first
    if (_optimizedWidgetCache.containsKey(key) && !forceFlattening) {
      return _optimizedWidgetCache[key]!;
    }

    // Build and optimize widget
    final originalWidget = widgetBuilder();
    final optimizedWidget = _flattenWidgetHierarchy(
      originalWidget,
      currentDepth: 0,
      maxDepth: effectiveMaxDepth,
    );

    // Calculate complexity score
    final complexityScore = _calculateWidgetComplexity(optimizedWidget);
    _widgetComplexityScores[key] = complexityScore;

    // Cache optimized widget
    _optimizedWidgetCache[key] = optimizedWidget;

    if (kDebugMode) {
      AnxLog.info(
        '🌳 Widget tree optimized: $key (complexity: $complexityScore)',
      );
    }

    return optimizedWidget;
  }

  /// Flatten widget hierarchy to reduce nesting
  static Widget _flattenWidgetHierarchy(Widget widget,
      {required int currentDepth, required int maxDepth}) {
    if (currentDepth >= maxDepth) {
      return widget;
    }

    // Handle specific widget types that can be flattened
    if (widget is Container) {
      return _optimizeContainer(widget, currentDepth, maxDepth);
    } else if (widget is Padding) {
      return _optimizePadding(widget, currentDepth, maxDepth);
    } else if (widget is Column || widget is Row) {
      return _optimizeFlex(widget, currentDepth, maxDepth);
    } else if (widget is Stack) {
      return _optimizeStack(widget, currentDepth, maxDepth);
    }

    return widget;
  }

  /// Optimize Container widgets by merging properties
  static Widget _optimizeContainer(
      Container container, int currentDepth, int maxDepth) {
    final child = container.child;

    // If child is also a Container, try to merge them
    if (child is Container) {
      // Simple merge - just combine padding and margin
      final mergedContainer = Container(
        key: container.key,
        alignment: container.alignment ?? child.alignment,
        padding: _mergePadding(container.padding, child.padding),
        margin: _mergeMargin(container.margin, child.margin),
        decoration: container.decoration ?? child.decoration,
        foregroundDecoration:
            container.foregroundDecoration ?? child.foregroundDecoration,
        constraints:
            _mergeConstraints(container.constraints, child.constraints),
        transform: container.transform ?? child.transform,
        transformAlignment:
            container.transformAlignment ?? child.transformAlignment,
        clipBehavior: container.clipBehavior,
        child: child.child,
      );

      return _flattenWidgetHierarchy(
        mergedContainer,
        currentDepth: currentDepth + 1,
        maxDepth: maxDepth,
      );
    }

    return container;
  }

  /// Optimize Padding widgets by merging with parent containers
  static Widget _optimizePadding(
      Padding padding, int currentDepth, int maxDepth) {
    final child = padding.child;

    // If child is a Container, merge padding into it
    if (child is Container) {
      final mergedContainer = Container(
        key: child.key,
        alignment: child.alignment,
        padding: _mergePadding(padding.padding, child.padding),
        margin: child.margin,
        decoration: child.decoration,
        foregroundDecoration: child.foregroundDecoration,
        constraints: child.constraints,
        transform: child.transform,
        transformAlignment: child.transformAlignment,
        clipBehavior: child.clipBehavior,
        child: child.child,
      );

      return _flattenWidgetHierarchy(
        mergedContainer,
        currentDepth: currentDepth + 1,
        maxDepth: maxDepth,
      );
    }

    return padding;
  }

  /// Optimize Flex widgets (Column/Row) by flattening nested flex widgets
  static Widget _optimizeFlex(Widget flex, int currentDepth, int maxDepth) {
    if (flex is Column) {
      return _optimizeColumn(flex, currentDepth, maxDepth);
    } else if (flex is Row) {
      return _optimizeRow(flex, currentDepth, maxDepth);
    }
    return flex;
  }

  /// Optimize Column widgets
  static Widget _optimizeColumn(Column column, int currentDepth, int maxDepth) {
    final optimizedChildren = <Widget>[];

    for (final child in column.children) {
      if (child is Column &&
          column.mainAxisAlignment == child.mainAxisAlignment &&
          column.crossAxisAlignment == child.crossAxisAlignment) {
        // Flatten nested columns with same alignment
        optimizedChildren.addAll(child.children);
      } else {
        optimizedChildren.add(_flattenWidgetHierarchy(
          child,
          currentDepth: currentDepth + 1,
          maxDepth: maxDepth,
        ));
      }
    }

    return Column(
      key: column.key,
      mainAxisAlignment: column.mainAxisAlignment,
      mainAxisSize: column.mainAxisSize,
      crossAxisAlignment: column.crossAxisAlignment,
      textDirection: column.textDirection,
      verticalDirection: column.verticalDirection,
      textBaseline: column.textBaseline,
      children: optimizedChildren,
    );
  }

  /// Optimize Row widgets
  static Widget _optimizeRow(Row row, int currentDepth, int maxDepth) {
    final optimizedChildren = <Widget>[];

    for (final child in row.children) {
      if (child is Row &&
          row.mainAxisAlignment == child.mainAxisAlignment &&
          row.crossAxisAlignment == child.crossAxisAlignment) {
        // Flatten nested rows with same alignment
        optimizedChildren.addAll(child.children);
      } else {
        optimizedChildren.add(_flattenWidgetHierarchy(
          child,
          currentDepth: currentDepth + 1,
          maxDepth: maxDepth,
        ));
      }
    }

    return Row(
      key: row.key,
      mainAxisAlignment: row.mainAxisAlignment,
      mainAxisSize: row.mainAxisSize,
      crossAxisAlignment: row.crossAxisAlignment,
      textDirection: row.textDirection,
      verticalDirection: row.verticalDirection,
      textBaseline: row.textBaseline,
      children: optimizedChildren,
    );
  }

  /// Optimize Stack widgets
  static Widget _optimizeStack(Stack stack, int currentDepth, int maxDepth) {
    final optimizedChildren = stack.children
        .map((child) => _flattenWidgetHierarchy(
              child,
              currentDepth: currentDepth + 1,
              maxDepth: maxDepth,
            ))
        .toList();

    return Stack(
      key: stack.key,
      alignment: stack.alignment,
      textDirection: stack.textDirection,
      fit: stack.fit,
      clipBehavior: stack.clipBehavior,
      children: optimizedChildren,
    );
  }

  /// Merge EdgeInsetsGeometry values
  static EdgeInsetsGeometry? _mergePadding(
    EdgeInsetsGeometry? padding1,
    EdgeInsetsGeometry? padding2,
  ) {
    if (padding1 == null) return padding2;
    if (padding2 == null) return padding1;

    // Simple addition for EdgeInsets
    if (padding1 is EdgeInsets && padding2 is EdgeInsets) {
      return EdgeInsets.fromLTRB(
        padding1.left + padding2.left,
        padding1.top + padding2.top,
        padding1.right + padding2.right,
        padding1.bottom + padding2.bottom,
      );
    }

    return padding1; // Fallback to first padding
  }

  /// Merge margin values
  static EdgeInsetsGeometry? _mergeMargin(
    EdgeInsetsGeometry? margin1,
    EdgeInsetsGeometry? margin2,
  ) {
    return _mergePadding(margin1, margin2); // Same logic as padding
  }

  /// Merge BoxConstraints
  static BoxConstraints? _mergeConstraints(
    BoxConstraints? constraints1,
    BoxConstraints? constraints2,
  ) {
    if (constraints1 == null) return constraints2;
    if (constraints2 == null) return constraints1;

    return BoxConstraints(
      minWidth: math.max(constraints1.minWidth, constraints2.minWidth),
      maxWidth: math.min(constraints1.maxWidth, constraints2.maxWidth),
      minHeight: math.max(constraints1.minHeight, constraints2.minHeight),
      maxHeight: math.min(constraints1.maxHeight, constraints2.maxHeight),
    );
  }

  /// Calculate widget complexity score
  static int _calculateWidgetComplexity(Widget widget) {
    int complexity = 1; // Base complexity

    if (widget is Container) {
      complexity += 2;
      if (widget.decoration != null) complexity += 3;
      if (widget.transform != null) complexity += 2;
    } else if (widget is Column || widget is Row) {
      final flex = widget as Flex;
      complexity += flex.children.length;
    } else if (widget is Stack) {
      complexity += widget.children.length * 2; // Stacks are more complex
    } else if (widget is RepaintBoundary) {
      complexity += 1; // RepaintBoundary adds slight complexity
    }

    return complexity;
  }

  /// Get widget tree optimization statistics
  static Map<String, dynamic> getOptimizationStats() {
    int totalComplexity = 0;
    int highComplexityWidgets = 0;

    for (final score in _widgetComplexityScores.values) {
      totalComplexity += score;
      if (score > _complexityThreshold) {
        highComplexityWidgets++;
      }
    }

    return {
      'totalOptimizedWidgets': _optimizedWidgetCache.length,
      'averageComplexity': _widgetComplexityScores.isNotEmpty
          ? totalComplexity / _widgetComplexityScores.length
          : 0.0,
      'highComplexityWidgets': highComplexityWidgets,
      'cacheHitRate': _optimizedWidgetCache.isNotEmpty ? 1.0 : 0.0,
    };
  }

  /// Clear optimization cache
  static void clearOptimizationCache() {
    final count = _optimizedWidgetCache.length;
    _optimizedWidgetCache.clear();
    _widgetComplexityScores.clear();

    if (kDebugMode) {
      AnxLog.info('🌳 Cleared widget tree optimization cache ($count widgets)');
    }
  }
}

/// Comprehensive validation system for NavigationRail rebuild reduction optimizations
class NavigationRailRebuildValidationSystem {
  static final Map<String, RebuildValidationMetrics> _validationMetrics = {};
  static final Map<String, DateTime> _lastValidationTimes = {};

  // Validation thresholds
  static const double _minMemoizationUtilization = 0.5; // 50% utilization

  /// Validate all NavigationRail rebuild reduction optimizations
  static Future<Map<String, dynamic>> validateRebuildOptimizations({
    required String railId,
    required BuildContext context,
  }) async {
    final results = <String, dynamic>{};

    try {
      // Test 1: Cross-platform compatibility validation (before async operations)
      final compatibilityResults = _validateCrossPlatformCompatibility(context);
      results['crossPlatform'] = compatibilityResults;

      // Test 2: Validate RepaintBoundary optimization
      final repaintResults = await _validateRepaintBoundaryOptimization(railId);
      results['repaintBoundary'] = repaintResults;

      // Test 3: Validate widget memoization
      final memoizationResults = _validateWidgetMemoization();
      results['memoization'] = memoizationResults;

      // Test 4: Validate state management optimization
      final stateResults = _validateStateManagement(railId);
      results['stateManagement'] = stateResults;

      // Test 5: Validate widget tree optimization
      final treeResults = _validateWidgetTreeOptimization();
      results['widgetTree'] = treeResults;

      // Test 6: Validate rebuild frequency reduction
      final rebuildResults = await _validateRebuildFrequency(railId);
      results['rebuildFrequency'] = rebuildResults;

      // Calculate overall optimization score
      final overallScore = _calculateOverallOptimizationScore(results);
      results['overallScore'] = overallScore;
      results['validationTime'] = DateTime.now().toIso8601String();

      // Store validation metrics
      _storeValidationMetrics(railId, results);

      // Log validation results
      if (kDebugMode) {
        _logValidationResults(railId, results);
      }
    } catch (e) {
      results['error'] = e.toString();
      AnxLog.severe('NavigationRail rebuild validation failed: $e');
    }

    return results;
  }

  /// Validate RepaintBoundary optimization effectiveness
  static Future<Map<String, dynamic>> _validateRepaintBoundaryOptimization(
      String railId) async {
    final stats = AdvancedRepaintBoundaryManager.getOptimizationStats();
    final totalComponents = stats['totalComponents'] as int;
    final optimizedComponents = stats['optimizedComponents'] as int;

    final optimizationRatio =
        totalComponents > 0 ? optimizedComponents / totalComponents : 0.0;
    final isEffective = optimizationRatio > 0.3; // 30% optimization target

    return {
      'totalComponents': totalComponents,
      'optimizedComponents': optimizedComponents,
      'optimizationRatio': optimizationRatio,
      'isEffective': isEffective,
      'targetRatio': 0.3,
    };
  }

  /// Validate widget memoization effectiveness
  static Map<String, dynamic> _validateWidgetMemoization() {
    final stats = NavigationRailRebuildReducer.getMemoizationStats();
    final totalMemoized = stats['totalMemoized'] as int;
    final cacheUtilization = stats['cacheUtilization'] as double;

    final isEffective =
        totalMemoized > 0 && cacheUtilization > _minMemoizationUtilization;

    return {
      'totalMemoized': totalMemoized,
      'cacheUtilization': cacheUtilization,
      'isEffective': isEffective,
      'targetUtilization': _minMemoizationUtilization,
    };
  }

  /// Validate state management optimization
  static Map<String, dynamic> _validateStateManagement(String railId) {
    final stats = NavigationRailStateManager.getStateStats();
    final totalStates = stats['totalStates'] as int;
    final activeStates = stats['activeStates'] as int;
    final totalListeners = stats['totalListeners'] as int;

    final stateEfficiency = totalStates > 0 ? activeStates / totalStates : 0.0;
    final isOptimized = stateEfficiency > 0.8 &&
        totalListeners < totalStates * 3; // Reasonable listener count

    return {
      'totalStates': totalStates,
      'activeStates': activeStates,
      'totalListeners': totalListeners,
      'stateEfficiency': stateEfficiency,
      'isOptimized': isOptimized,
      'targetEfficiency': 0.8,
    };
  }

  /// Validate widget tree optimization
  static Map<String, dynamic> _validateWidgetTreeOptimization() {
    final stats = NavigationRailWidgetTreeOptimizer.getOptimizationStats();
    final totalOptimized = stats['totalOptimizedWidgets'] as int;
    final averageComplexity = stats['averageComplexity'] as double;
    final highComplexityWidgets = stats['highComplexityWidgets'] as int;

    final isOptimized = totalOptimized > 0 &&
        averageComplexity < 50 &&
        highComplexityWidgets == 0;

    return {
      'totalOptimized': totalOptimized,
      'averageComplexity': averageComplexity,
      'highComplexityWidgets': highComplexityWidgets,
      'isOptimized': isOptimized,
      'targetComplexity': 50.0,
    };
  }

  /// Validate rebuild frequency reduction
  static Future<Map<String, dynamic>> _validateRebuildFrequency(
    String railId,
  ) async {
    // Simulate some navigation interactions to measure rebuilds
    final startTime = DateTime.now();
    int rebuildCount = 0;

    // This would normally involve actual user interactions or automated testing
    // For now, we'll use a simplified validation
    await Future<void>.delayed(const Duration(milliseconds: 100));

    final endTime = DateTime.now();
    final testDuration = endTime.difference(startTime);

    // Calculate rebuild rate (rebuilds per second)
    final rebuildRate = rebuildCount / testDuration.inSeconds;
    final isOptimal = rebuildRate < 5.0; // Less than 5 rebuilds per second

    return {
      'rebuildCount': rebuildCount,
      'testDuration': testDuration.inMilliseconds,
      'rebuildRate': rebuildRate,
      'isOptimal': isOptimal,
      'targetRate': 5.0,
    };
  }

  /// Validate cross-platform compatibility
  static Map<String, dynamic> _validateCrossPlatformCompatibility(
    BuildContext context,
  ) {
    final isTablet = DesignSystem.isTablet(context);
    final isLandscape =
        ResponsiveSystem.getOrientation(context) == Orientation.landscape;
    final isIOS = PlatformAdaptations.isIOS;

    // Check if optimizations are properly applied for current platform
    final shouldOptimize = isTablet && isLandscape;
    const isCompatible = true; // All optimizations should work cross-platform

    return {
      'isTablet': isTablet,
      'isLandscape': isLandscape,
      'isIOS': isIOS,
      'shouldOptimize': shouldOptimize,
      'isCompatible': isCompatible,
    };
  }

  /// Calculate overall optimization score (0-100)
  static double _calculateOverallOptimizationScore(
    Map<String, dynamic> results,
  ) {
    double score = 0.0;
    int testCount = 0;

    // RepaintBoundary score (20 points)
    final repaint = results['repaintBoundary'] as Map<String, dynamic>?;
    if (repaint != null && repaint['isEffective'] == true) {
      score += 20.0;
    }
    testCount++;

    // Memoization score (20 points)
    final memoization = results['memoization'] as Map<String, dynamic>?;
    if (memoization != null && memoization['isEffective'] == true) {
      score += 20.0;
    }
    testCount++;

    // State management score (20 points)
    final state = results['stateManagement'] as Map<String, dynamic>?;
    if (state != null && state['isOptimized'] == true) {
      score += 20.0;
    }
    testCount++;

    // Widget tree score (20 points)
    final tree = results['widgetTree'] as Map<String, dynamic>?;
    if (tree != null && tree['isOptimized'] == true) {
      score += 20.0;
    }
    testCount++;

    // Rebuild frequency score (20 points)
    final rebuild = results['rebuildFrequency'] as Map<String, dynamic>?;
    if (rebuild != null && rebuild['isOptimal'] == true) {
      score += 20.0;
    }
    testCount++;

    return testCount > 0 ? score : 0.0;
  }

  /// Store validation metrics for tracking
  static void _storeValidationMetrics(
    String railId,
    Map<String, dynamic> results,
  ) {
    final metrics = RebuildValidationMetrics(
      railId: railId,
      timestamp: DateTime.now(),
      overallScore: results['overallScore'] as double? ?? 0.0,
      results: results,
    );

    _validationMetrics[railId] = metrics;
    _lastValidationTimes[railId] = DateTime.now();
  }

  /// Log validation results for debugging
  static void _logValidationResults(
    String railId,
    Map<String, dynamic> results,
  ) {
    final score = results['overallScore'] as double;

    AnxLog.info(
      '🔍 NavigationRail Rebuild Optimization Validation ($railId):',
    );
    AnxLog.info('   Overall Score: ${score.toStringAsFixed(1)}/100');

    final repaint = results['repaintBoundary'] as Map<String, dynamic>?;
    if (repaint != null) {
      AnxLog.info(
        '   RepaintBoundary: ${repaint['optimizedComponents']}/${repaint['totalComponents']} optimized',
      );
    }

    final memoization = results['memoization'] as Map<String, dynamic>?;
    if (memoization != null) {
      final utilization = ((memoization['cacheUtilization'] as double) * 100)
          .toStringAsFixed(1);
      AnxLog.info(
        '   Memoization: ${memoization['totalMemoized']} widgets, $utilization% utilization',
      );
    }

    final state = results['stateManagement'] as Map<String, dynamic>?;
    if (state != null) {
      AnxLog.info(
        '   State Management: ${state['activeStates']}/${state['totalStates']} active states',
      );
    }

    final tree = results['widgetTree'] as Map<String, dynamic>?;
    if (tree != null) {
      AnxLog.info(
        '   Widget Tree: ${tree['totalOptimized']} optimized, avg complexity ${(tree['averageComplexity'] as double).toStringAsFixed(1)}',
      );
    }

    if (score >= 80) {
      AnxLog.info('   ✅ Excellent rebuild optimization performance!');
    } else if (score >= 60) {
      AnxLog.info('   ⚠️ Good optimization, room for improvement');
    } else {
      AnxLog.warning('   ❌ Rebuild optimization needs attention');
    }
  }

  /// Get validation history for performance tracking
  static Map<String, dynamic> getValidationHistory(String railId) {
    final metrics = _validationMetrics[railId];
    final lastValidation = _lastValidationTimes[railId];

    return {
      'railId': railId,
      'hasMetrics': metrics != null,
      'lastValidation': lastValidation?.toIso8601String(),
      'lastScore': metrics?.overallScore ?? 0.0,
    };
  }

  /// Clear validation history
  static void clearValidationHistory([String? railId]) {
    if (railId != null) {
      _validationMetrics.remove(railId);
      _lastValidationTimes.remove(railId);
    } else {
      _validationMetrics.clear();
      _lastValidationTimes.clear();
    }
  }
}

/// Validation metrics data class
class RebuildValidationMetrics {
  final String railId;
  final DateTime timestamp;
  final double overallScore;
  final Map<String, dynamic> results;

  RebuildValidationMetrics({
    required this.railId,
    required this.timestamp,
    required this.overallScore,
    required this.results,
  });
}

/// Enhanced Navigation Widgets for Dasso Reader
///
/// This file provides enhanced navigation components that use the NavigationSystem
/// for consistent, accessible, and platform-adaptive navigation.

// =====================================================
// ENHANCED TAB BAR
// =====================================================

/// Enhanced TabBar with improved visual feedback and accessibility
class EnhancedTabBar extends StatelessWidget implements PreferredSizeWidget {
  final List<NavigationDestination> destinations;
  final int currentIndex;
  final ValueChanged<int> onDestinationSelected;
  final bool isScrollable;
  final Color? backgroundColor;
  final Color? indicatorColor;

  const EnhancedTabBar({
    super.key,
    required this.destinations,
    required this.currentIndex,
    required this.onDestinationSelected,
    this.isScrollable = false,
    this.backgroundColor,
    this.indicatorColor,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: colorScheme.outlineVariant.withAlpha(128),
            width: DesignSystem.spaceMicro,
          ),
        ),
      ),
      child: TabBar(
        isScrollable: isScrollable,
        tabs: destinations.asMap().entries.map((entry) {
          final index = entry.key;
          final destination = entry.value;
          final isSelected = index == currentIndex;

          return NavigationSystem.createNavigationTab(
            context: context,
            destination: destination,
            isSelected: isSelected,
            onTap: () => onDestinationSelected(index),
          );
        }).toList(),
        labelColor: indicatorColor ?? colorScheme.primary,
        unselectedLabelColor: colorScheme.onSurfaceVariant,
        indicatorColor: indicatorColor ?? colorScheme.primary,
        indicatorWeight: DesignSystem.spaceXS - DesignSystem.spaceMicro,
        indicatorSize: TabBarIndicatorSize.tab,
        splashBorderRadius: BorderRadius.circular(DesignSystem.radiusM),
        overlayColor: WidgetStateProperty.resolveWith<Color?>((states) {
          if (states.contains(WidgetState.hovered)) {
            return colorScheme.primary.withAlpha((0.08 * 255).round());
          }
          if (states.contains(WidgetState.pressed)) {
            return colorScheme.primary.withAlpha((0.12 * 255).round());
          }
          return null;
        }),
        onTap: (index) {
          NavigationSystem.provideFeedback(NavigationFeedbackType.selection);
          onDestinationSelected(index);
        },
      ),
    );
  }

  @override
  Size get preferredSize =>
      const Size.fromHeight(DesignSystem.spaceXXL + DesignSystem.spaceL);
}

// =====================================================
// ENHANCED NAVIGATION RAIL
// =====================================================

/// Enhanced NavigationRail with improved visual feedback and responsive behavior
class EnhancedNavigationRail extends StatefulWidget {
  final List<NavigationDestination> destinations;
  final int currentIndex;
  final ValueChanged<int> onDestinationSelected;
  final bool extended;
  final Widget? leading;
  final Widget? trailing;
  final Color? backgroundColor;

  const EnhancedNavigationRail({
    super.key,
    required this.destinations,
    required this.currentIndex,
    required this.onDestinationSelected,
    this.extended = false,
    this.leading,
    this.trailing,
    this.backgroundColor,
  });

  @override
  State<EnhancedNavigationRail> createState() => _EnhancedNavigationRailState();
}

class _EnhancedNavigationRailState extends State<EnhancedNavigationRail>
    with RebuildOptimizationMixin {
  int _rebuildCount = 0;
  final String _railId = 'enhanced_navigation_rail';

  // Track previous widget state to detect rebuild triggers
  int? _previousCurrentIndex;
  bool? _previousExtended;
  int? _previousDestinationCount;
  Widget? _previousLeading;
  Widget? _previousTrailing;

  @override
  void initState() {
    super.initState();
    // Start performance profiling
    TabletPerformanceUtils.instance.startNavigationRailProfiling(_railId);

    // Initialize state management
    NavigationRailStateManager.updateState(
      _railId,
      currentIndex: widget.currentIndex,
      extended: widget.extended,
      destinations: widget.destinations,
      leading: widget.leading,
      trailing: widget.trailing,
    );

    // Create performance baseline
    WidgetsBinding.instance.addPostFrameCallback((_) {
      TabletPerformanceUtils.instance.createNavigationRailBaseline(
        _railId,
        destinationCount: widget.destinations.length,
        hasSearchBar: widget.leading != null,
        isExtended: widget.extended,
        context: context,
      );

      // Preload common widgets for better performance
      SideMenuComponentCache.preloadCommonWidgets(context);

      // Clear expired cache entries
      SideMenuComponentCache.clearExpired();

      // Clear expired memoizations
      NavigationRailRebuildReducer.clearExpiredMemoizations();

      // Clear expired states
      NavigationRailStateManager.clearExpiredStates();

      // Run validation after initialization (only in debug mode)
      if (kDebugMode) {
        _runOptimizationValidation();
        _runRebuildValidation();
      }
    });
  }

  /// Run optimization validation for performance monitoring
  void _runOptimizationValidation() {
    // Defer validation to avoid blocking initialization
    Future<void>.delayed(const Duration(seconds: 2)).then((_) async {
      if (mounted) {
        try {
          final results = await SideMenuOptimizationValidator
              .validateNavigationRailOptimizations(
            railId: _railId,
            context: context,
          );

          final score = results['overallScore'] as double? ?? 0.0;
          if (score < 60) {
            AnxLog.warning(
              '🔍 NavigationRail optimization score below target: ${score.toStringAsFixed(1)}/100',
            );
          }
        } catch (e) {
          AnxLog.warning('🔍 NavigationRail validation failed: $e');
        }
      }
    });
  }

  /// Run rebuild reduction validation
  void _runRebuildValidation() {
    // Defer validation to avoid blocking initialization
    Future<void>.delayed(const Duration(seconds: 5)).then((_) async {
      if (mounted) {
        try {
          final results = await NavigationRailRebuildValidationSystem
              .validateRebuildOptimizations(
            railId: _railId,
            context: context,
          );

          final score = results['overallScore'] as double? ?? 0.0;
          if (score < 70) {
            AnxLog.warning(
              '🔄 NavigationRail rebuild optimization score below target: ${score.toStringAsFixed(1)}/100',
            );
          } else {
            AnxLog.info(
              '🔄 NavigationRail rebuild optimization score: ${score.toStringAsFixed(1)}/100',
            );
          }
        } catch (e) {
          AnxLog.warning('🔄 NavigationRail rebuild validation failed: $e');
        }
      }
    });
  }

  /// Record advanced RepaintBoundary metrics for optimization
  void _recordAdvancedMetrics(DateTime renderStartTime) {
    // Defer metrics recording to avoid blocking current frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final renderTime = DateTime.now().difference(renderStartTime);

      // Record metrics for main NavigationRail
      AdvancedRepaintBoundaryManager.recordComponentMetrics(
        '${_railId}_main',
        renderTime: renderTime,
        rebuildCount: _rebuildCount,
        hadFrameDrops: false, // Would need frame rate monitoring to detect
        additionalData: {
          'destinationCount': widget.destinations.length,
          'hasLeading': widget.leading != null,
          'hasTrailing': widget.trailing != null,
          'isExtended': widget.extended,
        },
      );

      // Record metrics for individual components
      if (widget.leading != null) {
        AdvancedRepaintBoundaryManager.recordComponentMetrics(
          '${_railId}_leading',
          renderTime: renderTime,
          rebuildCount: _rebuildCount,
          hadFrameDrops: false,
          additionalData: {'componentType': 'leading'},
        );
      }

      if (widget.trailing != null) {
        AdvancedRepaintBoundaryManager.recordComponentMetrics(
          '${_railId}_trailing',
          renderTime: renderTime,
          rebuildCount: _rebuildCount,
          hadFrameDrops: false,
          additionalData: {'componentType': 'trailing'},
        );
      }

      // Record metrics for destinations
      for (int i = 0; i < widget.destinations.length; i++) {
        AdvancedRepaintBoundaryManager.recordComponentMetrics(
          '${_railId}_destination_$i',
          renderTime: renderTime,
          rebuildCount: _rebuildCount,
          hadFrameDrops: false,
          additionalData: {
            'componentType': 'destination',
            'destinationIndex': i,
            'isSelected': i == widget.currentIndex,
          },
        );
      }
    });
  }

  @override
  void dispose() {
    // Update baseline with final metrics
    TabletPerformanceUtils.instance.updateNavigationRailBaseline(
      _railId,
      rebuildCount: _rebuildCount,
      additionalMetrics: {
        'destinations': widget.destinations.length,
        'extended': widget.extended,
        'hasLeading': widget.leading != null,
        'hasTrailing': widget.trailing != null,
        'totalLifetime':
            DateTime.now().difference(DateTime.now()).inMilliseconds,
      },
    );

    // End performance profiling with final metrics
    TabletPerformanceUtils.instance.endNavigationRailProfiling(
      _railId,
      rebuildCount: _rebuildCount,
      additionalMetrics: {
        'destinations': widget.destinations.length,
        'extended': widget.extended,
        'hasLeading': widget.leading != null,
        'hasTrailing': widget.trailing != null,
      },
    );
    super.dispose();
  }

  /// Perform complexity analysis on the side menu
  void _performComplexityAnalysis(BuildContext context) {
    // Only perform analysis in debug mode and on tablets
    if (!kDebugMode || !DesignSystem.isTablet(context)) return;

    // Defer analysis to avoid blocking the current frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        final analysis =
            TabletPerformanceUtils.instance.analyzeSideMenuComplexity(context);

        // Log analysis results for development insights
        final complexityScore = analysis['overallComplexityScore'] as int? ?? 0;
        final recommendations =
            analysis['optimizationRecommendations'] as List<String>? ?? [];

        if (complexityScore > 60) {
          AnxLog.warning(
            '🔍 NavigationRail complexity score: $complexityScore/100',
          );
          if (recommendations.isNotEmpty) {
            AnxLog.info('💡 Optimization recommendations:');
            for (final recommendation in recommendations) {
              AnxLog.info('   • $recommendation');
            }
          }
        }
      } catch (e) {
        AnxLog.warning('🔍 NavigationRail complexity analysis failed: $e');
      }
    });
  }

  /// Detect what triggered this rebuild
  void _detectRebuildTriggers() {
    final triggers = <String>[];
    final context = <String, dynamic>{};

    // Check for navigation index changes
    if (_previousCurrentIndex != null &&
        _previousCurrentIndex != widget.currentIndex) {
      triggers.add('navigation_change');
      context['previousIndex'] = _previousCurrentIndex;
      context['newIndex'] = widget.currentIndex;
    }

    // Check for extended state changes
    if (_previousExtended != null && _previousExtended != widget.extended) {
      triggers.add('extended_change');
      context['previousExtended'] = _previousExtended;
      context['newExtended'] = widget.extended;
    }

    // Check for destination count changes
    if (_previousDestinationCount != null &&
        _previousDestinationCount != widget.destinations.length) {
      triggers.add('destinations_change');
      context['previousCount'] = _previousDestinationCount;
      context['newCount'] = widget.destinations.length;
    }

    // Check for leading widget changes (search bar, etc.)
    if (_previousLeading != widget.leading) {
      triggers.add('leading_change');
      context['hasLeading'] = widget.leading != null;
    }

    // Check for trailing widget changes
    if (_previousTrailing != widget.trailing) {
      triggers.add('trailing_change');
      context['hasTrailing'] = widget.trailing != null;
    }

    // If no specific trigger detected, it's likely a parent rebuild
    if (triggers.isEmpty && _rebuildCount > 1) {
      triggers.add('parent_rebuild');
    }

    // If this is the first rebuild, it's initialization
    if (_rebuildCount == 1) {
      triggers.add('initialization');
    }

    // Record all detected triggers
    for (final trigger in triggers) {
      TabletPerformanceUtils.instance.recordNavigationRailRebuild(
        _railId,
        trigger,
        context: context,
      );
    }

    // Update previous state for next comparison
    _updatePreviousState();
  }

  /// Update previous state tracking
  void _updatePreviousState() {
    _previousCurrentIndex = widget.currentIndex;
    _previousExtended = widget.extended;
    _previousDestinationCount = widget.destinations.length;
    _previousLeading = widget.leading;
    _previousTrailing = widget.trailing;
  }

  /// Build optimized destinations with RepaintBoundary and lazy loading
  List<NavigationRailDestination> _buildOptimizedDestinations(
    BuildContext context,
  ) {
    final destinations = <NavigationRailDestination>[];

    for (int i = 0; i < widget.destinations.length; i++) {
      final destination = widget.destinations[i];
      final key = '${_railId}_destination_$i';

      // Use lazy loader to get or create destination with advanced optimization
      final baseDestination = NavigationRailLazyLoader.getDestination(
        key,
        destination,
        context,
        forceReload: _rebuildCount <= 1, // Force reload on first build
      );

      // Create optimized destination with RepaintBoundary and memoization for components
      final optimizedDestination = NavigationRailDestination(
        icon: NavigationRailRebuildReducer.memoizeWidget(
          '${key}_icon',
          () => AdvancedRepaintBoundaryManager.optimizeWidget(
            '${key}_icon',
            baseDestination.icon,
          ),
          dependencies: [destination.icon, widget.currentIndex == i],
        ),
        selectedIcon: NavigationRailRebuildReducer.memoizeWidget(
          '${key}_selected_icon',
          () => AdvancedRepaintBoundaryManager.optimizeWidget(
            '${key}_selected_icon',
            baseDestination.selectedIcon,
          ),
          dependencies: [
            destination.getIcon(selected: true),
            widget.currentIndex == i,
          ],
        ),
        label: NavigationRailRebuildReducer.memoizeWidget(
          '${key}_label',
          () => AdvancedRepaintBoundaryManager.optimizeWidget(
            '${key}_label',
            baseDestination.label,
          ),
          dependencies: [destination.getLabel(context), widget.extended],
        ),
      );

      destinations.add(optimizedDestination);
    }

    // Log combined optimization statistics
    if (kDebugMode && _rebuildCount % 10 == 0) {
      final lazyStats = NavigationRailLazyLoader.getCacheStats();
      final cacheStats = SideMenuComponentCache.getStats();
      final memoStats = NavigationRailRebuildReducer.getMemoizationStats();

      // Calculate efficiency percentages
      final cachedDestinations = lazyStats['cachedDestinations'] as int;
      final cacheEfficiency = cachedDestinations > 0
          ? (cachedDestinations / widget.destinations.length * 100)
              .toStringAsFixed(1)
          : '0';
      final memoUtilization =
          ((memoStats['cacheUtilization'] as double) * 100).toStringAsFixed(1);

      AnxLog.info(
        '🔄 NavigationRail optimization stats:\n'
        '   Lazy cached destinations: $cachedDestinations\n'
        '   Component cache hits: ${cacheStats['totalCached']}\n'
        '   Memoized widgets: ${memoStats['totalMemoized']}\n'
        '   Cache efficiency: $cacheEfficiency%\n'
        '   Memoization utilization: $memoUtilization%',
      );
    }

    return destinations;
  }

  @override
  Widget buildOptimized(BuildContext context) {
    _rebuildCount++;
    final colorScheme = Theme.of(context).colorScheme;
    final renderStartTime = DateTime.now();

    // Update state management with current widget state
    NavigationRailStateManager.updateState(
      _railId,
      currentIndex: widget.currentIndex,
      extended: widget.extended,
      destinations: widget.destinations,
      leading: widget.leading,
      trailing: widget.trailing,
    );

    // Check if this rebuild is necessary based on state changes
    final currentState = NavigationRailStateManager.getState(_railId);

    if (kDebugMode && _rebuildCount % 20 == 0) {
      AnxLog.info('🔄 NavigationRail state hash: ${currentState.stateHash}');
    }

    // Detect and record rebuild triggers
    _detectRebuildTriggers();

    // Track NavigationRail metrics for performance monitoring
    TabletPerformanceUtils.instance.trackNavigationRailMetrics(
      menuItemCount: widget.destinations.length,
      hasSearchBar: widget.leading != null,
      isExpanded: widget.extended,
      activeAnimations: 0, // Will be updated when animations are detected
    );

    // Record advanced RepaintBoundary metrics
    _recordAdvancedMetrics(renderStartTime);

    // Perform complexity analysis (only on first few rebuilds to avoid spam)
    if (_rebuildCount <= 3) {
      _performComplexityAnalysis(context);
    }

    return RepaintBoundary(
      child: Container(
        decoration: BoxDecoration(
          color: widget.backgroundColor ?? colorScheme.surface,
          border: Border(
            right: BorderSide(
              color: colorScheme.outlineVariant.withAlpha(128),
              width: DesignSystem.spaceMicro,
            ),
          ),
        ),
        child: NavigationRail(
          extended: widget.extended,
          selectedIndex: widget.currentIndex,
          onDestinationSelected: (index) {
            NavigationSystem.provideFeedback(NavigationFeedbackType.selection);
            widget.onDestinationSelected(index);
          },
          leading: widget.leading != null
              ? AdvancedRepaintBoundaryManager.optimizeWidget(
                  '${_railId}_leading',
                  widget.leading!,
                )
              : null,
          trailing: widget.trailing != null
              ? AdvancedRepaintBoundaryManager.optimizeWidget(
                  '${_railId}_trailing',
                  widget.trailing!,
                )
              : null,
          destinations: _buildOptimizedDestinations(context),
          labelType: widget.extended
              ? NavigationRailLabelType.none
              : NavigationRailLabelType.all,
          backgroundColor: Colors.transparent,
          selectedIconTheme: IconThemeData(
            color: colorScheme.primary,
            size: AppIcons.sizeM,
          ),
          unselectedIconTheme: IconThemeData(
            color: colorScheme.onSurfaceVariant,
            size: AppIcons.sizeM,
          ),
          selectedLabelTextStyle: TextStyle(
            color: colorScheme.primary,
            fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600),
            fontSize: DesignSystem.fontSizeS,
          ),
          unselectedLabelTextStyle: TextStyle(
            color: colorScheme.onSurfaceVariant,
            fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w500),
            fontSize: DesignSystem.fontSizeS,
          ),
          indicatorColor: colorScheme.primaryContainer,
          indicatorShape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignSystem.radiusM),
          ),
        ),
      ),
    );
  }
}

// =====================================================
// ENHANCED BOTTOM NAVIGATION BAR
// =====================================================

/// Enhanced BottomNavigationBar with improved visual feedback and adaptive height
class EnhancedBottomNavigationBar extends StatelessWidget {
  final List<NavigationDestination> destinations;
  final int currentIndex;
  final ValueChanged<int> onDestinationSelected;
  final Color? backgroundColor;
  final double? height;
  final EdgeInsetsGeometry? margin;

  const EnhancedBottomNavigationBar({
    super.key,
    required this.destinations,
    required this.currentIndex,
    required this.onDestinationSelected,
    this.backgroundColor,
    this.height,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    // Calculate adaptive height matching your TabBar implementation
    final adaptiveHeight = height ?? _calculateAdaptiveHeight(context);

    // Use NavigationBar for Material 3 design with enhanced customization
    return Container(
      height: adaptiveHeight,
      margin: margin,
      child: NavigationBar(
        selectedIndex: currentIndex,
        onDestinationSelected: (index) {
          NavigationSystem.provideFeedback(NavigationFeedbackType.selection);
          onDestinationSelected(index);
        },
        backgroundColor: backgroundColor ?? colorScheme.surface,
        indicatorColor: colorScheme.primaryContainer,
        indicatorShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusM),
        ),
        destinations: destinations.map((destination) {
          return material.NavigationDestination(
            icon: Icon(
              destination.icon,
              size: AppIcons.sizeM, // Consistent with your design system
            ),
            selectedIcon: Icon(
              destination.getIcon(selected: true),
              size: AppIcons.sizeM,
            ),
            label: destination.getLabel(context),
            tooltip: destination.getTooltip(context),
          );
        }).toList(),
        labelBehavior: NavigationDestinationLabelBehavior.alwaysShow,
        animationDuration: DesignSystem.durationFast,
        // Enhanced elevation for better visual hierarchy with manufacturer adjustments
        elevation: DesignSystem.getAdjustedElevation(DesignSystem.elevationS),
      ),
    );
  }

  /// Calculate adaptive height matching the TabBar implementation
  double _calculateAdaptiveHeight(BuildContext context) {
    final screenSize = ResponsiveSystem.getScreenSize(context);
    final devicePixelRatio =
        screenSize.aspectRatio; // Use aspect ratio as proxy for density
    final textScaler = MediaQuery.textScalerOf(context);

    const baseHeight = DesignSystem.spaceXXL +
        DesignSystem.spaceL +
        DesignSystem.spaceTiny; // Match Material Design standard
    const minHeight = DesignSystem.widgetMinTouchTarget +
        DesignSystem.spaceXS; // Accessibility minimum

    // Scale for device density
    double densityFactor = (devicePixelRatio / 3.0).clamp(0.85, 1.25);

    // Scale for text size
    double textFactor = textScaler.scale(1.0).clamp(0.9, 1.3);

    // Device-specific adjustments
    double deviceFactor = 1.0;
    if (DesignSystem.isSmallPhone(context)) {
      deviceFactor = 0.9;
    } else if (DesignSystem.isTablet(context)) {
      deviceFactor = 1.1;
    }

    double calculatedHeight =
        baseHeight * densityFactor * textFactor * deviceFactor;

    // Ensure accessibility compliance
    return math.max(calculatedHeight, minHeight);
  }
}

// =====================================================
// ADAPTIVE NAVIGATION WRAPPER
// =====================================================

/// Adaptive navigation wrapper that chooses the appropriate navigation pattern
/// based on screen size and platform
class AdaptiveNavigationWrapper extends StatelessWidget {
  final List<NavigationDestination> destinations;
  final int currentIndex;
  final ValueChanged<int> onDestinationSelected;
  final Widget child;
  final Widget? leading;
  final Widget? trailing;
  final bool forceBottomNavigation;

  const AdaptiveNavigationWrapper({
    super.key,
    required this.destinations,
    required this.currentIndex,
    required this.onDestinationSelected,
    required this.child,
    this.leading,
    this.trailing,
    this.forceBottomNavigation = false,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Show side menu (NavigationRail) only for tablets in landscape orientation
        // or for desktop-sized screens (width-based fallback for desktop support)
        // Mobile devices always use bottom navigation
        final shouldUseRail = !forceBottomNavigation &&
            (constraints.maxWidth > DesignSystem.breakpointDesktop ||
                (DesignSystem.isTablet(context) &&
                    ResponsiveSystem.getOrientation(context) ==
                        Orientation.landscape));

        if (shouldUseRail) {
          // Use NavigationRail for larger screens
          final extended =
              constraints.maxWidth > DesignSystem.breakpointDesktop;

          return Scaffold(
            body: Row(
              children: [
                EnhancedNavigationRail(
                  destinations: destinations,
                  currentIndex: currentIndex,
                  onDestinationSelected: onDestinationSelected,
                  extended: extended,
                  leading: leading,
                  trailing: trailing,
                ),
                Expanded(child: child),
              ],
            ),
          );
        } else {
          // Use BottomNavigationBar for smaller screens
          return Scaffold(
            body: child,
            bottomNavigationBar: EnhancedBottomNavigationBar(
              destinations: destinations,
              currentIndex: currentIndex,
              onDestinationSelected: onDestinationSelected,
            ),
          );
        }
      },
    );
  }
}
